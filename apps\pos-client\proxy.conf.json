{"/api": {"target": "https://elevar-develop.frappe.cloud", "secure": true, "changeOrigin": true, "logLevel": "debug", "cookieDomainRewrite": {"*": ""}, "headers": {"Connection": "keep-alive"}, "onProxyRes": "function (proxyRes, req, res) { if (proxyRes.headers['set-cookie']) { const cookies = proxyRes.headers['set-cookie'].map(cookie => cookie.replace(/Domain=[^;]+;/, '')); proxyRes.headers['set-cookie'] = cookies; } }"}}