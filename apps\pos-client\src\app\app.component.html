@if(initialLoadIsDone){
  @if(toastVisible()) {
    <div class="fixed top-6 right-6 z-50 bg-green-600 text-white px-6 py-4 rounded-lg shadow-lg flex items-center gap-4 cursor-pointer transition-all animate-fade-in" (click)="dismissToast()" tabindex="0" (keyup.enter)="dismissToast()" (keyup.space)="dismissToast()">
      <div class="flex flex-col">
        <span class="font-semibold">New Transaction</span>
        <span class="text-sm">{{ latestTransaction()?.first_name }} ({{ latestTransaction()?.name }})</span>
        <span class="text-xs">KES {{ latestTransaction()?.trans_amount | number:'1.0-0' }}</span>
      </div>
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
    </div>
  }
  <lib-elevar-modal [isOpen]="isOpen()" [title]="title()" [description]="description()" [type]="type()">
  </lib-elevar-modal>
  <router-outlet></router-outlet>
} @else {
  <app-splash-screen />
}
