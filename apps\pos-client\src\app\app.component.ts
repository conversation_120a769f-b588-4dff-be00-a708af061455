import { Component, computed, inject, effect, signal } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SplashScreenComponent } from "./splash-screen/splash-screen.component";
import { ElevarModalComponent } from "@elements/ui";
import { NotificationService } from '@core';
import { AuthService, MpesaTransactionService } from '@elevar-clients/api';
import { MpesaTransaction } from '@models';
import { DecimalPipe } from '@angular/common';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterModule, SplashScreenComponent, ElevarModalComponent, DecimalPipe],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent {
  initialLoadIsDone = true; // Assume this is set appropriately elsewhere
  private readonly _authService = inject(AuthService);
  private readonly _notificationService = inject(NotificationService);

  isLoggedIn = this._authService.isLoggedIn()
  isOpen = computed(() => this._notificationService.NotificationState().isOpen);
  title = computed(() => this._notificationService.NotificationState().title);
  description = computed(() => this._notificationService.NotificationState().description);
  type = computed(() => this._notificationService.NotificationState().type);

  // Toast logic
  latestTransaction = signal<MpesaTransaction | null>(null);
  toastVisible = signal(false);
  toastTimeout: any;

  constructor(private readonly mpesaTransactionService: MpesaTransactionService) {
    setTimeout(() => {
      this.initialLoadIsDone = true;
    }, 3000);

    effect(() => {
      this.mpesaTransactionService.listenToNewTransactions().subscribe(async tx => {
        if (!tx.hasSeen) {
          this.latestTransaction.set(tx);
          this.toastVisible.set(true);
          clearTimeout(this.toastTimeout);
          this.toastTimeout = setTimeout(() => this.toastVisible.set(false), 5000);
          // Mark as seen in Firestore
          await this.mpesaTransactionService.markTransactionAsSeen(tx);
        }
      });
    });
  }

  dismissToast() {
    this.toastVisible.set(false);
  }
}
