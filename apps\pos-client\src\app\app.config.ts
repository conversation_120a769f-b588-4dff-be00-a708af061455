import { ApplicationConfig, provideZone<PERSON>hangeDetection, isDev<PERSON><PERSON>, Error<PERSON><PERSON><PERSON>, provideAppInitializer, inject } from '@angular/core';
import { provideRouter } from '@angular/router';
import { appRoutes } from './app.routes';
import { provideServiceWorker } from '@angular/service-worker';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { ElevarErrorHandler, errorInterceptor } from '@core';
import { tokenInterceptor } from '@elevar-clients/api';
import { OAuthService, OAuthStorage, OAuthModule, AuthConfig, provideOAuthClient } from 'angular-oauth2-oidc';

import { initializeApp, provideFirebaseApp } from '@angular/fire/app';
import { getAuth, provideAuth } from '@angular/fire/auth';
import { getFirestore, provideFirestore } from '@angular/fire/firestore';
import { getFunctions, provideFunctions } from '@angular/fire/functions';
import { getMessaging, provideMessaging } from '@angular/fire/messaging';
import { getStorage, provideStorage } from '@angular/fire/storage';

const firebaseConfig = {
  projectId: "elevar-a5347",
  appId: "1:670563079940:web:0b8c12cb1f56349c4ae251",
  storageBucket: "elevar-a5347.firebasestorage.app",
  apiKey: "AIzaSyCwdtUyYBVXQ7srUWC4baStfAE_tiNEafo",
  authDomain: "elevar-a5347.firebaseapp.com",
  messagingSenderId: "670563079940",
  measurementId: "G-2ZM39PJ3BY"
};

// Dynamically select OAuth config based on environment
function getOAuth2Config(): AuthConfig {
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    return {
      issuer: 'https://elevar-develop.frappe.cloud',
      clientId: 'hf4bcl5t9b',
      loginUrl: 'https://elevar-develop.frappe.cloud/api/method/frappe.integrations.oauth2.authorize',
      tokenEndpoint: 'https://elevar-develop.frappe.cloud/api/method/frappe.integrations.oauth2.get_token',
      userinfoEndpoint: 'https://elevar-develop.frappe.cloud/api/method/frappe.integrations.oauth2.openid_profile',
      redirectUri: 'http://localhost:4200/oauth2/callback',
      responseType: 'code',
      scope: 'all openid',
      showDebugInformation: true,
      useSilentRefresh: true,
      oidc: false
    };
  }
  return {
    issuer: 'https://elevar-develop.frappe.cloud',
    clientId: '2rafsbr989',
    loginUrl: 'https://elevar-develop.frappe.cloud/api/method/frappe.integrations.oauth2.authorize',
    tokenEndpoint: 'https://elevar-develop.frappe.cloud/api/method/frappe.integrations.oauth2.get_token',
    userinfoEndpoint: 'https://elevar-develop.frappe.cloud/api/method/frappe.integrations.oauth2.openid_profile',
    redirectUri: 'https://elevar-pos-staging.web.app/oauth2/callback',
    responseType: 'code',
    scope: 'openid all',
    showDebugInformation: true,
    useSilentRefresh: true,
    oidc: false
  };
}

function initializeOAuthApp(oauthService: OAuthService): () => Promise<boolean> {
  return () => {
    oauthService.configure(getOAuth2Config());
    return oauthService.tryLoginCodeFlow()
      .then(() => true)
      .catch(err => {
        console.error('Error initializing OAuth:', err);
        oauthService.initCodeFlow();
        throw err;
      });
  };
}

export function storageFactory(): OAuthStorage {
  return localStorage;
}

export const appConfig: ApplicationConfig = {
  providers: [
    OAuthModule.forRoot({
      resourceServer: {
        allowedUrls: ['https://elevar-develop.frappe.cloud/api'],
        sendAccessToken: true
      }
    }).providers!,
    provideHttpClient(withInterceptors([errorInterceptor, tokenInterceptor])),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(appRoutes),
    provideServiceWorker('ngsw-worker.js', {
      enabled: !isDevMode(),
      registrationStrategy: 'registerWhenStable:30000'
    }),
    provideOAuthClient(),
    { provide: OAuthStorage, useFactory: storageFactory },

    provideAppInitializer(() => {
      const oauthService = inject(OAuthService);
      return initializeOAuthApp(oauthService)();
    }),

    provideFirebaseApp(() => initializeApp(firebaseConfig)),
    provideAuth(() => getAuth()),
    provideFirestore(() => getFirestore()),
    provideFunctions(() => getFunctions()),
    provideMessaging(() => getMessaging()),
    provideStorage(() => getStorage()),
    { provide: ErrorHandler, useClass: ElevarErrorHandler },
  ],
};
