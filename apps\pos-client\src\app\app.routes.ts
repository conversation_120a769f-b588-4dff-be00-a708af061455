import { Route } from '@angular/router';
import { isLoggedInGuard} from '@elevar-clients/api';

import { OAuthCallbackComponent } from './pages/oauth-callback/oauth-callback.component';

export const appRoutes: Route[] = [
  {
    path: 'oauth2/callback',
    component: OAuthCallbackComponent,
    title: 'OAuth Callback'
  },
  {
    path: 'auth',
    loadChildren: () => import('@features/auth').then(c => c.routes),
    title: 'Elevar - Auth'
  },
  {
    path: 'inventory',
    loadChildren: () => import('@features/inventory').then(c => c.routes),
    title: 'Elevar - Inventory',
    canActivateChild:[isLoggedInGuard]
  },
  {
    path: 'user',
    loadChildren: () => import('@features/user').then(c => c.routes),
    title: 'Elevar - User',
    canActivateChild:[isLoggedInGuard]
  },
  {
    path: 'invoices',
    loadChildren: () => import('@features/invoices').then(c => c.routes),
    title: 'Elevar - Invoices',
    canActivateChild:[isLoggedInGuard]
  },
  {
    path: 'reports',
    loadChildren: () => import('@features/reports').then(c => c.routes),
    title: 'Elevar - Reports',
    canActivateChild:[isLoggedInGuard]
  },
  {
    path: '',
    redirectTo: '/auth',
    pathMatch: 'full'
  },
  {
    path: '**',
    redirectTo: '/auth',
    pathMatch: 'full'
  }
];
