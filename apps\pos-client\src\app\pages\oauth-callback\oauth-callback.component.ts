import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { OAuthService } from 'angular-oauth2-oidc';
import { inject } from '@angular/core';

import { AuthService } from '@elevar-clients/api';
import { LoaderComponent } from "@elements/ui";

@Component({
  selector: 'app-oauth-callback',
  template: `
    <lib-loader></lib-loader>
  `,
  imports: [LoaderComponent]
})
export class OAuthCallbackComponent {

  private readonly _authService = inject(AuthService);

  constructor(
    private readonly _router: Router,
    private readonly _oauthService: OAuthService
  ) {

    const isAuthenticated = this._oauthService.hasValidAccessToken();

    if (isAuthenticated) {
      this._authService.updateAuthState();
      // Redirect to invoices
      this._router.navigate(['/invoices']);
    } else {
      console.error('Lo<PERSON> failed - no valid access token');
      localStorage.clear(); // Clear any stored tokens
      this._router.navigate(['/auth/login']);
    }
  }
}
