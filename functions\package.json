{"name": "functions", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "deploy:frappe": "gcloud functions deploy frappeProxy --gen2 --runtime=nodejs20 --region=us-central1 --source=./ --entry-point=frappeProxy --trigger-http", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "axios": "^1.10.0"}, "devDependencies": {"typescript": "^4.9.0", "firebase-functions-test": "^3.1.0"}, "private": true}