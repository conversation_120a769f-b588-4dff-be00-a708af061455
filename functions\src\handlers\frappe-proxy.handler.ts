/**
 * Import necessary modules from Firebase and other libraries.
 */
import { onRequest } from "firebase-functions/v2/https";
import * as logger from "firebase-functions/logger";
import axios, { Method } from 'axios';
import { Request, Response } from 'express';

// The target backend URL that you want to proxy to.
const TARGET_BASE_URL = 'https://elevar-develop.frappe.cloud';

/**
 * An HTTP-triggered Cloud Function that acts as a proxy to the Frappe Cloud backend.
 * It uses the Firebase Functions v2 SDK.
 *
 * It forwards requests to the TARGET_BASE_URL, including methods, headers,
 * body, and query parameters.
 *
 * It correctly handles cookie-based authentication by forwarding the 'Cookie'
 * header from the client to the target and the 'Set-Cookie' header from the
 * target back to the client.
 */
export const frappeProxy = onRequest(
  // You can set options like region, memory, etc. here if needed.
  // For example: { region: 'us-central1', memory: '256MiB' }
  async (req: Request, res: Response) => {
    // 1. Log the start of the request for debugging and traceability.
    logger.info(`Starting frappeProxy request for: ${req.originalUrl}`, { structuredData: true });

    // 2. Set CORS headers to allow requests from your frontend (Angular app).
    //    - 'Access-Control-Allow-Origin': Allows all origins ('*') for development. Restrict in production.
    //    - 'Access-Control-Allow-Credentials': Allows cookies to be sent/received.
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Credentials', 'true');

    // 3. Handle CORS preflight (OPTIONS) requests:
    //    - Browsers send OPTIONS requests before certain requests to check permissions.
    //    - Respond with allowed methods and headers, then exit early.
    if (req.method === 'OPTIONS') {
      res.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
      res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie');
      res.set('Access-Control-Max-Age', '3600'); // Cache preflight response for 1 hour
      res.status(204).send('');
      return;
    }

    // 4. Build the full URL to the backend by combining the base URL and the original path/query.
    //    - This ensures the proxy forwards the request to the correct backend endpoint.
    const targetUrl = `${TARGET_BASE_URL}${req.originalUrl}`;

    logger.info(`Forwarding request to: ${targetUrl}`);

    // 5. Prepare headers to forward to the backend:
    //    - Start with a copy of the incoming request headers.
    //    - Remove or adjust headers that should not be forwarded (e.g., 'host', Google-internal headers).
    const forwardedHeaders: { [key: string]: any } = { ...req.headers };

    //    - Remove 'host' so axios sets it to the backend's hostname.
    delete forwardedHeaders['host'];

    //    - Remove Google/Firebase infrastructure headers that may cause issues or leak info.
    delete forwardedHeaders['x-forwarded-for'];
    delete forwardedHeaders['x-forwarded-proto'];
    delete forwardedHeaders['x-cloud-trace-context'];
    delete forwardedHeaders['function-execution-id'];
    delete forwardedHeaders['x-appengine-api-ticket'];
    delete forwardedHeaders['x-appengine-country'];

    try {
      // 6. Make the proxied HTTP request to the backend using axios:
      //    - Use the same HTTP method, URL, headers, and body as the original request.
      //    - Do not throw errors for non-2xx status codes (let the client handle them).
      //    - Do not follow redirects automatically (let the browser handle them).
      const backendResponse = await axios({
        method: req.method as Method,
        url: targetUrl,
        headers: forwardedHeaders,
        data: req.body,
        validateStatus: () => true,
        maxRedirects: 0
      });

      // 7. Handle cookies from the backend response:
      //    - If the backend sets cookies (e.g., for authentication), forward them to the client.
      //    - This is critical for login flows and session management.
      const setCookieHeader = backendResponse.headers['set-cookie'];
      if (setCookieHeader) {
        logger.info("Found 'set-cookie' header, forwarding to client.");
        res.setHeader('Set-Cookie', setCookieHeader);
      }

      // 8. Forward other headers from the backend response to the client:
      //    - Exclude 'set-cookie' (already handled), 'transfer-encoding', and 'content-encoding' (may cause issues).
      Object.keys(backendResponse.headers).forEach(key => {
        if (key.toLowerCase() !== 'set-cookie' && key.toLowerCase() !== 'transfer-encoding' && key.toLowerCase() !== 'content-encoding') {
            res.setHeader(key, backendResponse.headers[key]);
        }
      });

      // 9. Send the backend's status code and response body to the client:
      //    - This completes the proxying process, making it transparent to the frontend.
      logger.info(`Responding to client with status: ${backendResponse.status}`);
      res.status(backendResponse.status).send(backendResponse.data);

    } catch (error) {
      // 10. Handle errors that occur during the proxying process:
      //     - Log the error for debugging.
      //     - Respond with an appropriate status and message based on the error type.
      logger.error('Error proxying request:', error);

      if (axios.isAxiosError(error)) {
        if (error.response) {
            // The backend responded with an error status (e.g., 4xx, 5xx)
            res.status(error.response.status).send(error.response.data);
        } else if (error.request) {
            // The request was sent but no response was received from the backend
            res.status(504).send('Proxy timeout: No response from backend service.');
        }
      } else {
        // An unexpected error occurred while setting up the request
        res.status(500).send('An internal error occurred in the proxy function.');
      }
    }
});
