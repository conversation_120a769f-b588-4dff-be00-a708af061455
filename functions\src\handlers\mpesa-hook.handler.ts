import { onRequest } from "firebase-functions/v2/https";
import * as logger from "firebase-functions/logger";
import { MpesaTransaction } from '../interfaces/mpesa-transaction.interface';
import { initializeApp, applicationDefault } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';


initializeApp({ credential: applicationDefault() });
const db = getFirestore();

export const saveMpesaTransaction = onRequest(async (request, response) => {
  if (request.method !== 'POST') {
    response.status(405).send('Method Not Allowed');
    return;
  }

  const data = request.body as MpesaTransaction;
  if (!data?.trans_id) {
    response.status(400).send('Invalid input: trans_id is required');
    return;
  }

  try {
    await db.collection('transactions').doc(data.trans_id).set(data);
    response.status(200).send({ success: true, id: data.trans_id });
  } catch (error) {
    logger.error('Error saving transaction', error);
    response.status(500).send('Internal Server Error');
  }
});
