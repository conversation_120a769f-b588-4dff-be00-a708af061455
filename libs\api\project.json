{"name": "api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/api/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/api/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/api/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/api/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/api/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}