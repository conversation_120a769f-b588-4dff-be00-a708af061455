import { effect, inject, Injectable, signal, WritableSignal } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { BaseHttpService } from '@core';
import { Observable, lastValueFrom, map, tap } from 'rxjs';
import { Employee, EmployeeResponse } from '@models';
import { PosProfileService } from '../pos-profile/pos-profile.service';
import { encryptObject, decryptObject, ENCRYPTION_KEY } from '@core';

@Injectable({
  providedIn: 'root'
})
export class EmployeeService extends BaseHttpService<Employee> {
  private readonly _baseUrl = 'https://elevar-develop.frappe.cloud';
  private readonly _endpoint = 'api/resource/Employee';
  private readonly _posProfileService = inject(PosProfileService);

  loggedInEmployee: WritableSignal<Employee | null> = signal(null);

  constructor() {
    super();
    this.initializeService(this._endpoint, this._baseUrl);
    this.loggedInEmployee.set(decryptObject(localStorage.getItem('logged-in-employee') ?? '', ENCRYPTION_KEY));
    effect(() => {
      if (this.loggedInEmployee()) {
        this.setPOSProfile(this.loggedInEmployee() as Employee);
      }
    })

  }

  getEmployeeByUserId(user_id: string): Observable<Employee> {

    const params = new HttpParams()
      .set('filters', JSON.stringify([["user_id", "=", user_id]]))
      .set('fields', JSON.stringify([
        "first_name",
        "employee_name",
        "status",
        "company",
        "employee_number",
        "designation",
        "branch"
      ]));

    return this.http
      .get<EmployeeResponse>(this.apiUrl, {
        params,
        headers: this.getHeaders(),
        withCredentials: true
      })
      .pipe(
        map(response => response.data[0]),
        tap(res => {
          if (res) {
            this.loggedInEmployee.set(res);
            localStorage.setItem('logged-in-employee', encryptObject(res, ENCRYPTION_KEY));
          } else {
            this.loggedInEmployee.set(null);
            localStorage.removeItem('logged-in-employee');
          }
        })
      );
  }

  async setPOSProfile(employee: Employee) {
    return await lastValueFrom(this._posProfileService.getPOSProfileForEmployeeInBranch(employee))
  }

  async getUserInfo(): Promise<{ email: string, name: string }> {
    const response = await lastValueFrom(this.http.get(
      'https://elevar-develop.frappe.cloud/api/method/frappe.integrations.oauth2.openid_profile',
    ))
    return response as { email: string, name: string };
  }
}
