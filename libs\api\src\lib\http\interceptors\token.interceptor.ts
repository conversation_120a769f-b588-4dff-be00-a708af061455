import { HttpInterceptorFn, HttpRequest, HttpHandlerFn, HttpErrorResponse } from '@angular/common/http';
import { throwError, from } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';
import { OAuthService } from 'angular-oauth2-oidc';
import { inject } from '@angular/core';
import { Router } from '@angular/router';

export const tokenInterceptor: HttpInterceptorFn = (
  request: HttpRequest<unknown>,
  next: HttpHandlerFn
) => {
  const oauthService = inject(OAuthService);
  const router = inject(Router);

  // Add Authorization header for ERPNext API requests
  const accessToken = oauthService.getAccessToken();

  let modifiedRequest = request;

  if (accessToken && request.url.startsWith('https://elevar-develop.frappe.cloud/api/') && !request.url.endsWith('/api/method/frappe.integrations.oauth2.get_token')) {
    modifiedRequest = request.clone({
      setHeaders: {
        Authorization: `Bearer ${accessToken}`
      }
    });
  }

  return next(modifiedRequest).pipe(
    catchError((error: HttpErrorResponse) => {
      // Handle 401 Unauthorized errors
      if (error.status === 401) {
        return from(oauthService.refreshToken()).pipe(
          switchMap(() => {
            // Retry the original request with new token
            const newRequest = request.clone({
              setHeaders: {
                Authorization: `Bearer ${oauthService.getAccessToken()}`
              }
            });
            return next(newRequest);
          }),
          catchError(() => {
            // Refresh token failed, redirect to login
            console.log('Refresh token failed, redirecting to login');
            router.navigate(['auth/login']);
            return throwError(() => error);
          })
        );
      }
      return throwError(() => error);
    })
  );
};
