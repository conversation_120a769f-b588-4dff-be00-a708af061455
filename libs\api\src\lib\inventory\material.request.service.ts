import { inject, Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';

import { Observable } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { BaseHttpService } from '@core';

import { FilterOperator, MaterialRequest, MaterialRequestType, WorkflowState, MaterialRequestEntry } from '@models';

import { MaterialCartService } from './material.cart.service';
import { PosProfileService } from '../pos-profile/pos-profile.service';

@Injectable({
  providedIn: 'root'
})
export class MaterialRequestService extends BaseHttpService<any> {
  private readonly _baseUrl = 'https://elevar-develop.frappe.cloud';

  private readonly _posProfileService = inject(PosProfileService);
  private readonly materialService = inject(MaterialCartService);

  constructor() {
    super();
    this.initializeService('api/resource/Material Request', this._baseUrl);
  }

  getMaterialRequestsByCompany(
    limit_start = 0,
    limit_page_length = 20,
    order: 'asc' | 'desc' = 'desc'
  ): Observable<{ data: MaterialRequest[] }> {
    const fields = [
      "name",
      "transaction_date",
      "material_request_type",
      "creation",
      "workflow_state"
    ];

    const company = this._posProfileService.posProfile()?.company;
    const filters = [['company', FilterOperator.EQUALS, company]];

    let params = new HttpParams()
      .set('filters', JSON.stringify(filters))
      .set('fields', JSON.stringify(fields))
      .set('order_by', `transaction_date ${order}`);

    params = params
      .set('limit_start', limit_start.toString())
      .set('limit_page_length', limit_page_length.toString());

    return this.http
      .get<any>(this.apiUrl, {
        params,
        headers: this.getHeaders(),
        withCredentials: true
      })
      .pipe(
        map(response => ({ data: response.data })),
        catchError(this.handleError)
      );
  }

  createMaterialRequest(): Observable<MaterialRequestEntry> {
    const posProfile = this._posProfileService.posProfile();
    const materialItems = this.materialService.items().map(i => {
      return {
        item_code: i.item_code,
        qty: i.quantity
      }
    });
    const today = new Date().toISOString().split('T')[0];

    const payload = {
      workflow_state: WorkflowState.DRAFT,
      schedule_date: today,
      material_request_type: this.materialService.materialRequestType() ?? MaterialRequestType.PURCHASE,
      company: posProfile?.company,
      transaction_date: today,
      set_warehouse: posProfile?.warehouse,
      items: materialItems
    };

    return this.http
      .post<MaterialRequestEntry>(
        this.apiUrl,
        payload,
        {
          headers: this.getHeaders(),
          withCredentials: true
        }
      )
      .pipe(
        map(response => response),
        catchError(this.handleError)
      );
  }

  submitMaterialRequestAsPending(id: string): Observable<MaterialRequestEntry> {
    const url = `${this.apiUrl}/${id}`;
    const today = new Date().toISOString().split('T')[0];
    const payload = {
      data: {
        workflow_state: WorkflowState.PENDING,
        schedule_date: today
      }
    };

    return this.http
      .put<MaterialRequestEntry>(
        url,
        payload,
        {
          headers: this.getHeaders(),
          withCredentials: true
        }
      )
      .pipe(
        map(response => response),
        catchError(this.handleError)
      );
  }

  getMaterialRequestById(id: string): Observable<any> {
    const stockReconciliationUrl = this.apiUrl + '/' + id;
    return this.http.get<any>(stockReconciliationUrl, {
      headers: this.getHeaders(),
      withCredentials: true
    }).pipe(
      map(response => response.data),
      catchError(this.handleError)
    );
  }

}
