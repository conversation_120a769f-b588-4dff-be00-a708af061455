import { inject, Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';

import { Observable } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { BaseHttpService } from '@core';

import { FilterOperator, MaterialRequestType, WorkflowState } from '@models';

import { MaterialCartService } from './material.cart.service';
import { PosProfileService } from '../pos-profile/pos-profile.service';

@Injectable({
  providedIn: 'root'
})
export class MaterialTransferService extends BaseHttpService<any> {
  private readonly _baseUrl = 'https://elevar-develop.frappe.cloud';

  private readonly _posProfileService = inject(PosProfileService);
  private readonly _materialService = inject(MaterialCartService);

  constructor() {
    super();
    this.initializeService('api/resource/Stock Entry', this._baseUrl);
  }

  getMaterialTransfersByCompany(
    limit_start = 0,
    limit_page_length = 20,
    order: 'asc' | 'desc' = 'desc'
  ): Observable<{ data: any[] }> {
    const fields = [
      "name",
      "posting_date",
      "posting_time",
      "purpose",
      "workflow_state",
    ];

    const company = this._posProfileService.posProfile()?.company;
    const filters = [['company', FilterOperator.EQUALS, company]];

    let params = new HttpParams()
      .set('filters', JSON.stringify(filters))
      .set('fields', JSON.stringify(fields))
      .set('order_by', `posting_date ${order}`);

    params = params
      .set('limit_start', limit_start.toString())
      .set('limit_page_length', limit_page_length.toString());

    return this.http
      .get<any>(this.apiUrl, {
        params,
        headers: this.getHeaders(),
        withCredentials: true
      })
      .pipe(
        map(response => ({ data: response.data })),
        catchError(this.handleError)
      );
  }

  createMaterialRequest() {
    const posProfile = this._posProfileService.posProfile();
    const materialItems = this._materialService.items().map(i => {
      return {
        item_code: i.item_code,
        qty: i.quantity
      }
    })
    const today = new Date().toISOString().split('T')[0];

    const payload = {
      workflow_state: WorkflowState.DRAFT,
      schedule_date: today,
      material_request_type: this._materialService.materialRequestType() ?? MaterialRequestType.PURCHASE,
      company: posProfile?.company,
      transaction_date: today,
      set_warehouse: posProfile?.warehouse,
      items: materialItems
    }

    return this.http.post<any>(
        this.apiUrl,
        payload,
        {
          headers: this.getHeaders(),
          withCredentials: true
        }
      ).pipe(
        catchError(this.handleError)
      );
  }

  getMaterialRequestById(id: string): Observable<any> {
    const stockReconciliationUrl = this.apiUrl + '/' + id;
    return this.http.get<any>(stockReconciliationUrl, {
      headers: this.getHeaders(),
      withCredentials: true
    }).pipe(
      map(response => response.data),
      catchError(this.handleError)
    );
  }

  /**
   * Update the workflow_state of a material transfer (Stock Entry)
   * @param id The Stock Entry ID
   * @param workflowState The new workflow state ('Received' | 'Rejected')
   */
  updateMaterialTransfer(id: string, workflowState: 'Received' | 'Rejected') {
    const url = `${this.apiUrl}/${id}`;
    const body = {
      data: {
        workflow_state: workflowState
      }
    };
    return this.http.put<any>(url, body, {
      headers: this.getHeaders(),
      withCredentials: true
    }).pipe(
      catchError(this.handleError)
    );
  }

}
