import { HttpParams } from "@angular/common/http";
import { inject, Injectable } from "@angular/core";
import { BaseHttpService, NotificationService } from "@core";
import { FilterCondition, FilterOperator, FrappeListResponse, Item, ItemPrice, ItemResponse, POSProfile } from "@models";
import { catchError, map, Observable, switchMap, tap } from "rxjs";
import { PosProfileService } from "../pos-profile/pos-profile.service";
import { ItemGroupService } from "./items-group.service";

@Injectable({
  providedIn: 'root'
})
export class ItemService extends BaseHttpService<ItemResponse> {
  private readonly _posProfileServic = inject(PosProfileService);
  private readonly _notificationService = inject(NotificationService);
  private readonly _baseUrl = 'https://elevar-develop.frappe.cloud';
  private readonly _itemsGroupService = inject(ItemGroupService);


  constructor() {
    super();
    this.initializeService('api/resource/Item', this._baseUrl);
  }

  getItemsByGroup(itemGroup: string): Observable<Item[]> {
    return this._getRawItems([itemGroup]);
  }

  private _getRawItems(itemGroups: string[], pageStart = 0, pageLength = 20,): Observable<Item[]> {
    const fields = [
      'item_code',
      'item_name',
      'description',
      'item_group',
      'stock_uom',
      'disabled',
      'image',
      'stock_uom',
    ];

    const orFilters = itemGroups.map(group => ['item_group', FilterOperator.EQUALS, group]);
    
    const params = new HttpParams()
      .set('fields', JSON.stringify(fields))
      .set('or_filters', JSON.stringify(orFilters))
      .set('limit_start', pageStart.toString())
      .set('limit_page_length', pageLength.toString())
      .set('order_by', 'item_name desc');

    return this.http.get<ItemResponse>(`${this.apiUrl}`, {
        params,
        headers: this.getHeaders(),
        withCredentials: true
      })
      .pipe(
        map(response => response.data),
        catchError(this.handleError)
      );
  }

  /**
   * Get item prices for the current POS profile
   * @param pageStart Starting index for pagination (default: 0)
   * @param pageLength Number of items to fetch (default: 20)
   * @returns Observable array of ItemPrice objects
   */
  getItemPrices(pageStart = 0, pageLength = 20, customPriceList?: string): Observable<ItemPrice[]> {
    const fields = [
      'item_code',
      'item_name',
      'price_list_rate',
      'price_list'
    ];

    const posProfile = this._posProfileServic.posProfile();

    if (!posProfile) {
      this._notificationService.setError('Error fetching prices', 'POS profile not found. Log out and try again.');
      throw new Error('POS profile not found');
    }
  
    const filters: FilterCondition[] = [['price_list', FilterOperator.EQUALS, customPriceList ?? posProfile.selling_price_list]];
  
    const params = new HttpParams()
      .set('fields', JSON.stringify(fields))
      .set('filters', JSON.stringify(filters))
      .set('limit_start', pageStart.toString())
      .set('limit_page_length', pageLength.toString())
      .set('order_by', 'creation desc');

    return this.http.get<FrappeListResponse<ItemPrice>>(`${this.apiUrl} Price`, {
      params,
      headers: this.getHeaders(),
      withCredentials: true
    }).pipe(
      map(response => response.data),
      catchError(this.handleError)
    );
  }

  /**
   * Fetch items by item groups and enrich them with prices from the POS profile's price list
   * @param itemGroups Array of item group names to filter items
   * @param pageStart Starting index for pagination (default: 0)
   * @param pageLength Number of items to fetch (default: 20)
   * @returns Observable array of Items with price field populated
   */
  getItems(itemGroups: string[], pageStart = 0, pageLength = 20): Observable<Item[]> {
    return this._getRawItems(itemGroups, pageStart, pageLength).pipe(
      switchMap(items => {
        return this.getItemPrices(pageStart, pageLength).pipe(
          map(prices => {
            // Create a map of item_code to price_list_rate for efficient lookup
            const priceMap = new Map<string, number>();
            prices.forEach(price => priceMap.set(price.item_code, price.price_list_rate));

            // Update each item's price field with the corresponding price_list_rate
            const res =  items.map(item => ({
              ...item,
              price: priceMap.get(item.item_code) ?? item.price 
            }));

            return res;
          })
        );
      }),
      catchError(this.handleError)
    );
  }

  getAllItems() {
    return this._itemsGroupService.getSubgroups('Grocery').pipe(
      switchMap(res => this.getItems(res.map(r => r.name))), 
      tap(res => console.log(res)),
      map(items => items)
    );
  }
    
}