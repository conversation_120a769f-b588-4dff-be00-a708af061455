import { Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { Observable, map } from 'rxjs';
import { BaseHttpService } from '@core';

@Injectable({
  providedIn: 'root'
})
export class ModeOfPaymentsService extends BaseHttpService<any> {
  private readonly _baseUrl = 'https://elevar-develop.frappe.cloud';

  constructor() {
    super();
    this.initializeService('api/resource/Mode of Payment', this._baseUrl);
  }

  getAllEnabledModesOfPayment(): Observable<any[]> {
    const filters = JSON.stringify([["enabled","=","1"],["type","in",["Cash","Phone"]]]);
    const params = new HttpParams().set('filters', filters);
    return this.http.get<{ data: any[] }>(this.apiUrl, { params, headers: this.getHeaders(), withCredentials: true })
      .pipe(map(res => res.data));
  }
}

