import { inject, Injectable } from '@angular/core';
import { HttpHeaders, HttpParams } from '@angular/common/http';
import { BaseHttpService } from '@core';
import { Observable, map } from 'rxjs';
import { FrappeListResponse, MpesaTransaction, MpesaTransactionDetails } from '@models';
import { Firestore, collection, addDoc, collectionChanges, CollectionReference, DocumentData, doc, updateDoc } from '@angular/fire/firestore';

@Injectable({
  providedIn: 'root'
})
export class MpesaTransactionService extends BaseHttpService<FrappeListResponse<MpesaTransaction>> {
  private readonly _firestore = inject(Firestore);
  private readonly _baseUrl = 'https://elevar-develop.frappe.cloud';
  private readonly _endpoint = 'api/resource/M-Pesa Payment Confirmation';

  constructor() {
    super();
    this.initializeService(this._endpoint, this._baseUrl);
  }

  protected override getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded'
    });
  }

  getTransactions({
    filters,
    order_by,
    fields
  }: {
    filters: any[];
    order_by?: string;
    fields?: string[];
  }): Observable<MpesaTransaction[]> {
    let params = new HttpParams()
      .set('filters', JSON.stringify(filters));
    if (order_by) {
      params = params.set('order_by', order_by);
    }
    if (fields) {
      params = params.set('fields', JSON.stringify(fields));
    }
    return this.http
      .get<FrappeListResponse<MpesaTransaction>>(
        `${this.apiUrl}`,
        { params, headers: this.getHeaders(), withCredentials: true }
      )
      .pipe(map(response => response.data || []));
  }

  /**
   * Get details of a single Mpesa transaction by name (ID)
   */
  getTransactionByName(name: string): Observable<MpesaTransactionDetails> {
    const url = `${this.apiUrl}/${encodeURIComponent(name)}`;
    return this.http
      .get<{ data: MpesaTransactionDetails }>(
        url,
        { headers: this.getHeaders(), withCredentials: true }
      )
      .pipe(map(response => response.data));
  }

  /**
   * Firestore: Listen to real-time additions in the 'transactions' collection
   */
  listenToNewTransactions(): Observable<MpesaTransaction> {
    const transactionsRef = collection(this._firestore, 'transactions');
    return new Observable<MpesaTransaction>(subscriber => {
      const sub = collectionChanges(transactionsRef).subscribe(changes => {
        changes.forEach(change => {
          if (change.type === 'added') {
            subscriber.next(change.doc.data() as MpesaTransaction);
          }
        });
      });
      return () => sub.unsubscribe();
    });
  }

  /**
   * Firestore: Add a new transaction to the 'transactions' collection
   */
  addTransactionToFirestore(transaction: MpesaTransaction): Promise<void> {
    const transactionsRef = collection(this._firestore, 'transactions');
    return addDoc(transactionsRef, transaction).then(() => undefined);
  }

  /**
   * Mark a transaction as seen in Firestore by setting hasSeen: true
   */
  async markTransactionAsSeen(transaction: MpesaTransaction): Promise<void> {
    // If 'name' is the Firestore doc ID
    const transactionDocRef = doc(this._firestore, 'transactions', transaction.name);
    await updateDoc(transactionDocRef, { hasSeen: true });
  }
}
