{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "declaration": true, "declarationMap": true, "inlineSources": true, "types": []}, "exclude": ["src/**/*.spec.ts", "src/test-setup.ts", "jest.config.ts", "src/**/*.test.ts"], "include": ["src/**/*.ts", "src/lib/auth/oauth2.callback.component.ts", "src/lib/auth/index.ts", "src/lib/auth/types.ts", "src/lib/auth/oauth2.config.ts", "src/lib/auth/oauth2.callback.service.ts", "../core/src/lib/http/inteceptors/token.interceptor.ts"]}