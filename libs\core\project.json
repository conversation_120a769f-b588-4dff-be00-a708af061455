{"name": "core", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/core/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/core/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/core/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/core/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/core/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}