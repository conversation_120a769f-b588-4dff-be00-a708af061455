import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'dateDifference'
})
export class DateDifferencePipe implements PipeTransform {

  transform(targetDateStr: string, targetTimeStr?: string): number {
    // Validate inputs
    if (!targetDateStr) {
      return 0;
    }

    try {
      const targetDate = new Date(targetDateStr);

      // Check if the date is valid
      if (isNaN(targetDate.getTime())) {
        return 0;
      }

      const currentDate = new Date();
      const differenceInMs = currentDate.getTime() - targetDate.getTime();
      const differenceInDays = Math.floor(differenceInMs / (1000 * 60 * 60 * 24));

      // If we have a time string and the date difference is 0 or negative (same day or future)
      if (targetTimeStr && differenceInDays <= 0) {
        try {
          // Clean the time string - remove microseconds and quotes
          const cleanedTime = targetTimeStr.trim().replace(/^["']|["']$/g, '').split('.')[0];

          // Create a proper datetime string
          const targetDateTime = new Date(`${targetDateStr}T${cleanedTime}`);

          // Check if the datetime is valid
          if (isNaN(targetDateTime.getTime())) {
            return differenceInDays;
          }

          const diffInMs = currentDate.getTime() - targetDateTime.getTime();
          const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));

          // Return hours if less than 24 hours, otherwise return days
          if (diffInMs > 0 && diffInHours < 24) {
            return diffInHours;
          }
        } catch (timeError) {
          console.warn('Error parsing time in dateDifference pipe:', timeError);
          return differenceInDays;
        }
      }

      return differenceInDays;
    } catch (error) {
      console.warn('Error in dateDifference pipe:', error);
      return 0;
    }
  }
}
