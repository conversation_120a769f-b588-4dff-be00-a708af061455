import { Injectable, signal, computed } from '@angular/core';
import { NotificationState } from '@models';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private readonly _notificationState = signal<NotificationState>({
    type: 'success',
    title: '',
    description: '',
    isOpen: false
  });

  readonly NotificationState = computed(() => this._notificationState());

  setError(title: string, description: string) {
    this._notificationState.set({
      type: 'error',
      title,
      description,
      isOpen: true
    });
  }

  setSuccess(title: string, description: string) {
    this._notificationState.set({
      type:'success',
      title,
      description,
      isOpen: true
    });
  }

  setWarning(title: string, description: string) {
    this._notificationState.set({
      type: 'warning',
      title,
      description,
      isOpen: true
    });
  }

  clearNotification() {
    this._notificationState.set({
      type: 'success',
      title: '',
      description: '',
      isOpen: false
    });
  }


}
