import { HttpInterceptorFn, HttpResponse, HttpErrorResponse } from '@angular/common/http';
import { catchError, tap } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { inject, isDevMode } from '@angular/core';
import { CookieService } from 'ngx-cookie-service';
import { Router } from '@angular/router';
import { encryptObject } from '../../util/encrypt.util';
import { ENCRYPTION_KEY } from '../../util/encryption-key';

export const cookieInterceptor: HttpInterceptorFn = (req, next) => {
  const cookieService = inject(CookieService);
  const router = inject(Router);

  return next(req.clone({
    withCredentials: true
  })).pipe(
    tap(event => {
      if (event instanceof HttpResponse) {
        const loggedInUser = cookieService.getAll();
        localStorage.setItem('current-user', encryptObject(loggedInUser, ENCRYPTION_KEY));
      }
    }),
    catchError((error: HttpErrorResponse) => {
      if (isDevMode()) {
        console.error('Interceptor Error:', error);
      }

      if (error.status === 403) {
        localStorage.clear();
        cookieService.deleteAll();
        router.navigate(['/auth/login']);
      }

      return throwError(() => error);
    })
  );
};
