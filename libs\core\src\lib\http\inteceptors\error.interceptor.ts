import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { inject } from '@angular/core';
import { NotificationService } from '../../helpers/services/notification.service';

export const errorInterceptor: HttpInterceptorFn = (req, next) => {
  const notificationService = inject(NotificationService);

  return next(req).pipe(
    catchError((error: HttpErrorResponse) => {
      let title = 'Error';
      let description = 'An unexpected error occurred';

      switch (error.status) {
        case 400:
          title = 'Bad Request';
          description = 'Please check your input data';
          break;
        case 401:
          title = 'Unauthorized';
          description = 'Please log in again';
          break;
        case 403:
          title = 'Forbidden';
          description = 'You don\'t have permission to access this resource';
          break;
        case 404:
          title = 'Not Found';
          description = 'The requested resource was not found';
          break;
        case 500:
          title = 'Server Error';
          description = 'Something went wrong on our end. Please try again later';
          break;
        default:
          if (error.error?.message) {
            description = error.error.message;
          }
      }

      notificationService.setError(title, description);
      return throwError(() => error);
    })
  );
};