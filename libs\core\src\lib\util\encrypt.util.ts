import * as CryptoJS from 'crypto-js';

/**
 * Encrypts data using AES encryption with a provided key.
 * @param data The string data to encrypt.
 * @param key The encryption key.
 * @returns The encrypted string.
 */
export function encryptData(data: string, key: string): string {
  if (!key) {
    throw new Error('Encryption key is missing.');
  }
  return CryptoJS.AES.encrypt(data, key).toString();
}

/**
 * Decrypts data using AES encryption with a provided key.
 * @param encryptedData The encrypted string to decrypt.
 * @param key The encryption key.
 * @returns The decrypted string or null if decryption fails.
 */
export function decryptData(encryptedData: string, key: string): string | null {
  if (!key) {
    console.error('Decryption key is missing.');
    return null;
  }
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, key);
    const decryptedText = bytes.toString(CryptoJS.enc.Utf8);
    // If the decrypted text is empty, it means decryption likely failed
    if (!decryptedText) {
      return null;
    }
    return decryptedText;
  } catch (error) {
    console.error('Failed to decrypt cache data:', error);
    return null;
  }
}

/**
 * Encrypts any serializable object.
 */
export function encryptObject<T>(obj: T, key: string): string {
  return encryptData(JSON.stringify(obj), key);
}

/**
 * Decrypts to an object of type T.
 */
export function decryptObject<T>(encrypted: string, key: string): T | null {
  const decrypted = decryptData(encrypted, key);
  if (!decrypted) return null;
  try {
    return JSON.parse(decrypted) as T;
  } catch {
    return null;
  }
}
