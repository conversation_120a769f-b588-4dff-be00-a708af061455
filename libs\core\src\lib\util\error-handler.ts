import { LocationStrategy, PathLocationStrategy } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import { <PERSON>rror<PERSON>and<PERSON>, inject, Injector } from "@angular/core";

export class ElevarErrorHandler implements ErrorHandler {
  private readonly _injector = inject(Injector);
  handleError(error: any): void {
    const location = this._injector.get(LocationStrategy);
    const url = location instanceof PathLocationStrategy ? location.path() : '';

    const message = error.message ? error.message : error.toString();
    const stack = error instanceof HttpErrorResponse ? null : error.stack;

    // Log the error to your preferred logging service
    console.error({
      timestamp: new Date().toISOString(),
      message: message,
      url: url,
      stack: stack,
    });
  }
}
