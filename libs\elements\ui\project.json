{"name": "ui", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/elements/ui/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/elements/ui/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/elements/ui/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/elements/ui/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/elements/ui/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}