import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ButtonComponent, ElevarInputsComponent, PageFooterComponent, PageHeaderComponent, ProductCardComponent, SearchInputComponent } from '.';
import { MpesaDetailsCardComponent } from './lib/cards/mpesa-details-card/mpesa-details-card.component';
import { NgxSonnerToaster } from 'ngx-sonner';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonComponent,
    PageHeaderComponent,
    PageFooterComponent,
    SearchInputComponent,
    ElevarInputsComponent,
    ProductCardComponent,
    MpesaDetailsCardComponent,
    NgxSonnerToaster
  ],
  exports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonComponent,
    PageHeaderComponent,
    PageFooterComponent,
    SearchInputComponent,
    ElevarInputsComponent,
    ProductCardComponent,
    MpesaDetailsCardComponent
  ]
})
export class SharedModule {}
