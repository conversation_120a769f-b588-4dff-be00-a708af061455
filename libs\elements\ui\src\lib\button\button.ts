import { Component, Input, HostBinding } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-button',
  imports: [CommonModule],
  templateUrl: './button.html',
  styleUrls: ['./button.scss'],
})
export class ButtonComponent {
  @Input() buttonType: 'default' | 'secondary-rounded' | 'dark' | 'success-btn' | 'light' | 'green' | 'red' | 'yellow' | 'purple' | 'notify-green' | 'notify-blue' | 'disabled' | 'loading' | 'outline-red' = 'default';
  @Input() disabled = false;

  private readonly _baseClasses = 'font-medium focus:outline-none py-2 px-6';
  private readonly _defaultPadding = 'py-2 mt-5 px-6';
  private readonly _roundedClasses = 'rounded-full';
  private readonly _defaultRounded = 'rounded';
  private readonly _focusRingClasses = 'focus:ring-2 focus:ring-offset-2';

  @HostBinding('class') get class() {
    switch (this.buttonType) {
      case 'default':
        return `${this._baseClasses} ${this._defaultPadding} ${this._defaultRounded} bg-primary-lightGreen text-white hover:bg-primary-hoverGreen ${this._focusRingClasses} focus:ring-hoverGreen`;
      case 'secondary-rounded':
        return `${this._baseClasses} ${this._roundedClasses} bg-primary-lightGreen text-white hover:bg-primary-hoverGreen ${this._focusRingClasses} focus:ring-hoverGreen`;
      case 'notify-green':
        return `${this._baseClasses} text-sm px-5 py-2.5 me-2 mb-2 rounded-lg text-white bg-gray-800 hover:bg-gray-900 focus:ring-4 focus:ring-gray-300 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700`;
      case 'notify-blue':
        return `${this._baseClasses} ${this._roundedClasses} text-primary-darkBlue bg-primary-lightBlue hover:bg-primary-hoverBlue hover:text-white ${this._focusRingClasses} focus:ring-hoverBlue`;
      case 'success-btn':
        return `${this._baseClasses}  ${this._roundedClasses} text-primary-success-green-text bg-primary-success-green-background hover:bg-primary-lightGreen hover:text-white ${this._focusRingClasses} focus:ring-hoverGreen`;
      case 'disabled':
        return `${this._baseClasses} ${this._defaultPadding} rounded-lg text-sm px-5   bg-gray-400 text-gray-700 cursor-not-allowed focus:ring-0`;
      case 'loading':
        return `${this._baseClasses} relative text-sm px-5 py-2.5 me-2 mb-2 rounded-lg bg-blue-500 text-white focus:ring-2 focus:ring-blue-300 opacity-70 cursor-wait`;
      case 'red':
        return `${this._baseClasses} ${this._defaultPadding} ${this._defaultRounded} border border-red-600 text-white bg-red-600 hover:bg-red-700 focus:ring-2 focus:ring-red-300`;
      case 'outline-red':
        return `${this._baseClasses} ${this._defaultPadding} ${this._defaultRounded} border border-red-600 text-red-600 bg-white hover:bg-red-50 focus:ring-2 focus:ring-red-300`;
      default:
        return '';
    }
  }
}
