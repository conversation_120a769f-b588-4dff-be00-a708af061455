<div class=" bg-gray-100 p-4 flex items-center justify-center">
    <div class="w-full max-w-sm relative bg-white rounded-2xl p-6 border-2 border-gray-200 transition-all duration-300 hover:border-primary-hoverGreen hover:shadow-lg hover:shadow-black/20 group">
      <div class="text-center mb-8">
        <h2 class="text-2xl font-bold text-primary-lightGreen mb-2">Card title</h2>
        <p class="text-gray-600">One example of interactive card.</p>
        We can use this for products.
      </div>
      <button class="w-full bg-primary-hoverGreen text-white text-base font-medium rounded-xl py-3 px-4 transition-all duration-300 ease-out transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100">
        More info
      </button>
    </div>
  </div>