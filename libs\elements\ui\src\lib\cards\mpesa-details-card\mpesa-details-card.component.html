<div class="bg-primary-cardGray rounded-lg p-4 shadow mb-4 cursor-pointer hover:bg-gray-100 transition" (click)="viewDetails()" tabindex="0" (keyup.enter)="viewDetails()">
  <div class="flex justify-between items-start mb-2">
    <h3 class="font-semibold text-gray-900">{{ statement.first_name }}</h3>
    <span [class]="'px-3 py-1 rounded-full text-sm ' + getStatusColor('success')">
      KES {{ statement.trans_amount | number:'1.0-0' }}
    </span>
  </div>
  <div class="flex justify-between items-center mb-2">
    <span class="text-gray-600">Shortcode: {{ statement.business_shortcode }}</span>
    <span class="text-xs text-gray-500">ID: {{ statement.trans_id }}</span>
  </div>
  <div class="flex justify-between items-center mb-2">
    <span></span>
    <span class="text-xs text-gray-500">Ref: {{ statement.name }}</span>
  </div>
</div>
