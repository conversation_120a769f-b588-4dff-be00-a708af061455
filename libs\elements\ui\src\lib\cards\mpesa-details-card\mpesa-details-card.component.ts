import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MpesaTransaction } from '@models';
import { Router } from '@angular/router';

@Component({
  selector: 'lib-mpesa-details-card',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './mpesa-details-card.component.html',
  styleUrl: './mpesa-details-card.component.scss'
})
export class MpesaDetailsCardComponent {
  @Input() statement!: MpesaTransaction;
  showMpesaDetails = false;

  constructor(private router: Router) {}

  viewDetails() {
    if (this.statement?.name) {
      this.router.navigate(['/reports/transaction', this.statement.name]);
    }
  }

  getStatusColor(status: string): string {
    return 'bg-green-100 text-green-800 border-green-200';
  }
}
