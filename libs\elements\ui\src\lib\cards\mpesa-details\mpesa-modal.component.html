<div class="fixed inset-0 bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
      <div class="flex items-center justify-between p-6 border-b border-gray-100">
        <h2 class="text-lg font-semibold text-gray-900">Transaction Details</h2>
        <button class="p-1 hover:bg-gray-100 rounded-full transition-colors" (click)="close.emit()">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
          </svg>          
        </button>
      </div>
  
      <div class="p-6">
        <div class="text-center mb-8">
          <div class="text-3xl font-bold text-gray-900 mb-1">KES {{ statement.amount | number:'1.0-0' }}</div>
          <div class="text-sm text-gray-500">Transaction Amount</div>
        </div>
  
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">Transaction Code</span>
            <span class="text-sm font-medium text-gray-900">{{ statement.id }}</span>
          </div>
  
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">Status</span>
            <span class="text-sm font-medium text-green-600">{{ statement.status }}</span>
          </div>
  
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">Customer</span>
            <span class="text-sm font-medium text-gray-900">{{ statement.name }}</span>
          </div>
  
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">Phone Number</span>
            <span class="text-sm font-medium text-gray-900">+254 751 814351</span>
          </div>
  
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">Date</span>
            <span class="text-sm font-medium text-gray-900">May 20, 2025</span>
          </div>
  
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">Time</span>
            <span class="text-sm font-medium text-gray-900">2:05 PM</span>
          </div>
  
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">M-Pesa Shortcode</span>
            <span class="text-sm font-medium text-gray-900">174379</span>
          </div>
  
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">Allocation Status</span>
            <span class="text-sm font-medium text-green-600">Allocated</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  