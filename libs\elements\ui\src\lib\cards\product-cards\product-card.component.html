
<div class="bg-primary-cardGray rounded-lg p-4 shadow mb-4">
  @switch(type) {
    @case('invoice') {
      <div (click)="navigateToInvoiceDetail.emit(document.name)"
           (keyup.enter)="navigateToInvoiceDetail.emit(document.name)"
           tabindex="0"
           class="flex justify-between items-start mb-2">
        <h3 class="font-semibold text-gray-900">{{ document.name }}</h3>
        <span class="text-sm text-gray-500">{{ document.posting_date | date:'dd/MM/yyyy hh:mm' }}</span>

      </div>
      <div class="flex justify-between items-center mb-2">
        <span class="text-gray-600">{{ document.customer }}</span>
        <span class="text-sm text-gray-600">{{ document.rounded_total }}</span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-gray-900">{{ document.currency }}{{ document.grand_total }}</span>
        <span [class]="'px-3 py-1 rounded-full text-sm ' + getStatusColor(document.status)">
          {{ document.status | titlecase }}
        </span>
      </div>
    }
    @case('reconc') {
      <div class="flex justify-between items-start mb-2">
        <h3 class="font-semibold text-gray-900">{{ document.name }}</h3>
        <span class="text-sm text-gray-600">{{ document.posting_date | date:'dd/MM/yyyy hh:mm' }}</span>
      </div>
      @if(showLocation){
        <div class="flex justify-between items-center mb-2">
          <span class="text-gray-600">{{ document.location }} </span>
        </div>
      }
      <div class="flex justify-between items-center mb-2">
        <span class="text-gray-600">{{ document.itemCount || document.purpose }} items</span>
        <span [class]="'px-3 py-1 rounded-full text-sm ' + getStatusColor(document.workflow_state)">
         {{ document.workflow_state | titlecase }}
        </span>
      </div>
    }
    @case('stock-reconc') {
      <div class="flex justify-between items-start mb-2">
        <h3 class="font-semibold text-gray-900">{{ document.name }}</h3>
        <span class="text-sm text-gray-600">{{ document.posting_date | date:'dd/MM/yyyy hh:mm' }}</span>
      </div>
      <div class="flex justify-between items-center mb-2">
        <span class="text-gray-600">Status:</span>
        <span [class]="'px-3 py-1 rounded-full text-sm ' + getStatusColor(document.workflow_state)">
          {{ document.workflow_state }}
        </span>
      </div>
    }
    @case('request') {
      <div class="flex justify-between items-start mb-2">
        <h3 class="font-semibold text-gray-900">{{ document.name }}</h3>
        <span class="text-sm text-gray-600">{{ document.creation | date:'dd/MM/yyyy hh:mm' }}</span>
      </div>
      @if(showLocation){
        <div class="flex justify-between items-center mb-2">
          <span class="text-gray-600">{{  document.location }}</span>
        </div>
      }
      <div class="flex justify-between items-center mb-2">
        <span class="text-gray-600">{{ document.material_request_type }} items</span>
        <span [class]="'px-3 py-1 rounded-full text-sm ' + getStatusColor(document.workflow_state)">
         {{ document.workflow_state }}
        </span>
      </div>
    }
    @case('material-transfer') {
      <div class="flex justify-between items-start mb-2">
        <h3 class="font-semibold text-gray-900">{{ document.name }}</h3>
        <span class="text-sm text-gray-600">{{ document.posting_date }} {{ document.posting_time }}</span>
      </div>
      <div class="flex justify-between items-center mb-2">
        <span class="text-gray-600">Purpose: {{ document.purpose }}</span>
        <span [class]="'px-3 py-1 rounded-full text-sm ' + getStatusColor(document.workflow_state)">
          {{ document.workflow_state }}
        </span>
      </div>
    }
  }
</div>

