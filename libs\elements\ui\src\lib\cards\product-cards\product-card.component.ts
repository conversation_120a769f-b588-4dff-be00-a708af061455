import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

import { POSInvoice } from '@models';

@Component({
  selector: 'lib-product-card',
  imports: [CommonModule],
  templateUrl: './product-card.component.html',
})
export class ProductCardComponent {

  @Input() document!: POSInvoice | any; //TODO: Remove any
  @Input() type!: string;
  @Input() showLocation = false;
  @Output() navigateToInvoiceDetail = new EventEmitter<string>();


  getStatusColor(status: string): string {
    // Use the 'type' property to determine which mapping to use
    switch (this.type) {
      case 'invoice':
        // Invoice status mapping
        switch ((status || '').toLowerCase()) {
          case 'paid':
            return 'bg-green-100 text-green-700';
          case 'draft':
            return 'bg-gray-200 text-gray-700';
          case 'submitted':
            return 'bg-blue-100 text-blue-700';
          case 'cancelled':
            return 'bg-red-100 text-red-700';
          case 'overdue':
            return 'bg-orange-100 text-orange-700';
          case 'unpaid':
            return 'bg-yellow-100 text-yellow-700';
          case 'partially paid':
            return 'bg-amber-100 text-amber-700';
          case 'return':
            return 'bg-red-100 text-red-700';
          case 'refunded':
            return 'bg-purple-100 text-purple-700';
          case 'void':
            return 'bg-gray-100 text-gray-600';
          case 'disputed':
            return 'bg-orange-100 text-orange-700';
          case 'pending':
            return 'bg-yellow-100 text-yellow-700';
          case 'approved':
            return 'bg-green-100 text-green-700';
          case 'rejected':
            return 'bg-red-100 text-red-700';
          case 'closed':
            return 'bg-gray-100 text-gray-600';
          default:
            return 'bg-gray-100 text-gray-600';
        }
      case 'stock-reconc':
        // Stock Reconciliation status mapping
        switch ((status || '').toLowerCase()) {
          case 'approved':
            return 'bg-green-100 text-green-700';
          case 'pending approval':
            return 'bg-yellow-100 text-yellow-700';
          case 'draft':
            return 'bg-gray-200 text-gray-700';
          default:
            return 'bg-gray-100 text-gray-600';
        }
      case 'request':
        // Material Request status mapping
        switch ((status || '').toLowerCase()) {
          case 'draft':
            return 'bg-gray-200 text-gray-700';
          case 'pending approval':
            return 'bg-yellow-100 text-yellow-700';
          case 'approved':
            return 'bg-green-100 text-green-700';
          case 'accepted for transfer':
            return 'bg-blue-100 text-blue-700';
          case 'rejected':
            return 'bg-red-100 text-red-700';
          case 'cancelled':
            return 'bg-red-100 text-red-700';
          case 'completed':
            return 'bg-green-100 text-green-700';
          case 'partially ordered':
            return 'bg-orange-100 text-orange-700';
          case 'ordered':
            return 'bg-purple-100 text-purple-700';
          case 'received':
            return 'bg-green-100 text-green-700';
          case 'pending':
            return 'bg-yellow-100 text-yellow-700';
          case 'in progress':
            return 'bg-blue-100 text-blue-700';
          case 'submitted':
            return 'bg-indigo-100 text-indigo-700';
          case 'under review':
            return 'bg-amber-100 text-amber-700';
          case 'on hold':
            return 'bg-orange-100 text-orange-700';
          case 'closed':
            return 'bg-gray-100 text-gray-600';
          default:
            return 'bg-gray-100 text-gray-600';
        }
      case 'material-transfer':
        // Material Transfer (Stock Entry) status mapping
        switch ((status || '').toLowerCase()) {
          case 'received':
            return 'bg-green-100 text-green-700';
          case 'cancelled':
            return 'bg-red-100 text-red-700';
          default:
            return 'bg-gray-100 text-gray-600';
        }
      default:
        return 'bg-gray-100 text-gray-600';
    }
  }


}
