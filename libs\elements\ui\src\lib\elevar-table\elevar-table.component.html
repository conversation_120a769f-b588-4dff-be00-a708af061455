<div class="overflow-hidden rounded-lg border border-gray-200">
  <table class="w-full">
    <thead>
      <tr>
        <th *ngFor="let col of columns" 
            class="bg-primary-greenLight px-6 py-3 text-left text-lg font-medium text-white"
            [style.width]="col.width"
            [class.cursor-pointer]="sortable && col.sortable"
            (click)="col.sortable ? sortBy(col.field) : null">
          {{col.header}}
          <span *ngIf="sortField === col.field" class="sort-icon ml-2">
            {{sortOrder === 'asc' ? '↑' : '↓'}}
          </span>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of paginatedItems">
        <td *ngFor="let col of columns" 
            class="border-t border-gray-200 px-6 py-4">
          <ng-container [ngSwitch]="col.type">
            <img *ngSwitchCase="'image'" 
                 [src]="item[col.field]" 
                 [alt]="item[col.field]"
                 class="h-10 w-10 object-cover rounded">
            <span *ngSwitchCase="'date'">
              {{item[col.field] | date:'mediumDate'}}
            </span>
            <span *ngSwitchCase="'boolean'">
              {{item[col.field] ? 'Yes' : 'No'}}
            </span>
            <span *ngSwitchDefault>{{item[col.field]}}</span>
          </ng-container>
        </td>
      </tr>
      <tr *ngIf="!items?.length">
        <td [attr.colspan]="columns.length" 
            class="border-t border-gray-200 px-6 py-4 text-center text-gray-500">
          No data available
        </td>
      </tr>
    </tbody>
  </table>

  <div *ngIf="showPagination && totalPages > 1" 
       class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
    <div>
      <p class="text-sm text-gray-700">
        Showing 
        <span class="font-medium">{{startIndex + 1}}</span>
        to
        <span class="font-medium">{{endIndex}}</span>
        of
        <span class="font-medium">{{items.length}}</span>
        results
      </p>
    </div>
    <div>
      <button [disabled]="currentPage === 1"
              (click)="currentPage = currentPage - 1"
              class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
        Previous
      </button>
      <button [disabled]="currentPage === totalPages"
              (click)="currentPage = currentPage + 1"
              class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
        Next
      </button>
    </div>
  </div>
</div>