import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableColumn } from '../../models/table-column.model';

@Component({
  selector: 'lib-elevar-table',
  imports: [CommonModule],
  templateUrl: './elevar-table.component.html',
  styleUrl: './elevar-table.component.scss'
})

export class ElevarTableComponent {
  @Input() items!: any[];
  @Input() columns!: TableColumn[];
  @Input() loading = false;
  @Input() sortable = true;
  @Input() showPagination = true;
  @Input() pageSize = 10;

  sortField?: string;
  sortOrder: 'asc' | 'desc' = 'asc';
  currentPage = 1;

  sortBy(field: string): void {
    if (this.sortable && field) {
      if (this.sortField === field) {
        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
      } else {
        this.sortField = field;
        this.sortOrder = 'asc';
      }

      this.items = [...this.items].sort((a, b) => {
        const comparison = a[field] < b[field] ? -1 : 1;
        return this.sortOrder === 'asc' ? comparison : -comparison;
      });
    }
  }

  get paginatedItems(): any[] {
    const start = (this.currentPage - 1) * this.pageSize;
    return this.items.slice(start, start + this.pageSize);
  }

  get totalPages(): number {
    return Math.ceil(this.items.length / this.pageSize);
  }

  get startIndex(): number {
    return (this.currentPage - 1) * this.pageSize;
  }

  get endIndex(): number {
    return Math.min(this.startIndex + this.pageSize, this.items.length);
  }
}
