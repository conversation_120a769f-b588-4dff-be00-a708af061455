import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-elevar-checkbox',
  imports: [CommonModule],
  templateUrl: './elevar-checkbox.component.html',
  styleUrl: './elevar-checkbox.component.scss'
})

// Standalone Reusable checkbox component.
export class ElevarCheckboxComponent {
  @Input() label = '';
  @Input() checked = false;
  @Output() checkedChange = new EventEmitter<boolean>();

  onChanged(event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;
    this.checkedChange.emit(isChecked);
  }
}
