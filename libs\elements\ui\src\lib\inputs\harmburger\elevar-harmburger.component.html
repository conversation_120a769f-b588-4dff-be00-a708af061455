<label class="block relative cursor-pointer w-[50px] h-[40px]" for="menuToggle">
    <input type="checkbox" id="menuToggle" class="hidden" [(ngModel)]="isChecked" (change)="toggleMenu()">
  
    <span class="absolute w-[45px] h-[7px] bg-primary-lightGreen rounded-full inline-block transition-all duration-300 ease-in-out left-0"
          [ngClass]="{'top-0': !isChecked, 'rotate-45 origin-top-left w-[48px] left-[5px]': isChecked}"></span>
    <span class="absolute w-[45px] h-[7px] bg-primary-lightGreen rounded-full inline-block transition-all duration-300 ease-in-out left-0 top-[17px]"
          [ngClass]="{'opacity-100': !isChecked, 'translate-x-[-20px] opacity-0': isChecked}"></span>
    <span class="absolute w-[45px] h-[7px] bg-primary-lightGreen rounded-full inline-block transition-all duration-300 ease-in-out left-0"
          [ngClass]="{'bottom-0': !isChecked, '-rotate-45 origin-top-left w-[48px] bottom-[-1px] shadow-lg': isChecked}"></span>
  </label>
  