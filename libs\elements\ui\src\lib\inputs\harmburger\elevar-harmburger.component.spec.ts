import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ElevarHarmburgerComponent } from './elevar-harmburger.component';

describe('ElevarHarmburgerComponent', () => {
  let component: ElevarHarmburgerComponent;
  let fixture: ComponentFixture<ElevarHarmburgerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ElevarHarmburgerComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(ElevarHarmburgerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
