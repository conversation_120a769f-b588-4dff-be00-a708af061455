import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'lib-elevar-harmburger',
  imports: [CommonModule, FormsModule,  ReactiveFormsModule],
  templateUrl: './elevar-harmburger.component.html',
  styleUrl: './elevar-harmburger.component.scss'
})
export class ElevarHarmburgerComponent {
  isChecked = false;

  toggleMenu() {
    this.isChecked = !this.isChecked;
  }
}
