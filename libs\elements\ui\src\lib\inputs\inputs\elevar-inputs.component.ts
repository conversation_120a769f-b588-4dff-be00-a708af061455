import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { SelectOption } from '../../../models/select.model';


@Component({
  selector: 'lib-elevar-inputs',
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './elevar-inputs.component.html',
  styleUrl: './elevar-inputs.component.scss'
})
export class ElevarInputsComponent {
  @Input() inputId = '';
  @Input() control = new FormControl();
  @Input() label = '';
  @Input() type: 'text' | 'selector' | 'number' | 'date' | 'datetime' | 'time' | 'password' = "text";
  @Input() placeholder?: string;
  @Input() options: SelectOption[] = [];
  @Input() disabled = false;
  @Input() useOriginalObject = false;
  @Input() step?: string; // Allow decimal step for number inputs

  showPassword = false;
  isFocused = false;

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  onFocus() {
    this.isFocused = true;
  }

  onBlur() {
    this.isFocused = false;
  }

  get isFloating(): boolean {
    return this.isFocused || !!this.control.value;
  }

  getPlaceholder(): string {
    if (this.placeholder) {
      return this.placeholder;
    }

    const controlName = this.controlName;
    switch (controlName) {
      case 'username':
        return 'Enter your username';
      case 'email':
        return 'Enter your email';
      case 'password':
        return 'Enter your password';
      default:
        return 'Enter a value';
    }
  }

  get controlName(): string | null {
    const form = this.control.parent as FormGroup | null;
    if (!form) return null;

    return Object.keys(form.controls).find(name => form.controls[name] === this.control) ?? null;
  }
}
