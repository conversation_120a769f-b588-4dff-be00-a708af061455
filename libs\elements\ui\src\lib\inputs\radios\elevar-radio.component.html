<h1>Reusable Radios</h1>
<div class="flex gap-10">
    <ng-container *ngFor="let option of options">
      <div class="inline-flex items-center">
        <label class="relative flex items-center cursor-pointer" [for]="option.value">
          <input
            [name]="name"
            type="radio"
            class="peer h-5 w-5 cursor-pointer appearance-none rounded-full border border-primary-lightGreen checked:border-primary-lightGreenl transition-all"
            [id]="option.value"
            [value]="option.value"
            [checked]="option.value === selectedValue"
            (change)="onSelectionChange(option.value)"
          />
          <span
            class="absolute bg-primary-hoverGreen w-3 h-3 rounded-full opacity-0 peer-checked:opacity-100 transition-opacity duration-200 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          ></span>
        </label>
        <label class="ml-2 text-slate-600 cursor-pointer text-sm" [for]="option.value">
          {{ option.label }}
        </label>
      </div>
    </ng-container>
</div>