import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-elevar-radio',
  imports: [CommonModule],
  templateUrl: './elevar-radio.component.html',
  styleUrl: './elevar-radio.component.scss'
})
export class ElevarRadioComponent {
  @Input() options: { label: string; value: string }[] = [];
  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @Input() name: string = 'radio-group';
  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @Input() selectedValue: string = '';
  @Output() selectionChange = new EventEmitter<string>();

  onSelectionChange(value: string) {
    this.selectionChange.emit(value);
  }
}
