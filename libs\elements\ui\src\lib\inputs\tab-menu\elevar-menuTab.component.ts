import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface MenuItem {
  name: string;
  description: string;
  price: number;
}

@Component({
  selector: 'lib-elevar-menu-tab',
  imports: [CommonModule],
  templateUrl: './elevar-menuTab.component.html',
  styleUrl: './elevar-menuTab.component.scss'
})
export class ElevarMenuTabComponent {
  categories: string[] = ['Appetizers', 'Main Courses', 'Desserts', 'Drinks'];
  activeCategory = 'Appetizers';

  menuItems: Record<string, MenuItem[]> = {
    'Appetizers': [
      { name: '<PERSON><PERSON><PERSON><PERSON>', description: 'Toasted bread with tomatoes, garlic, and basil', price: 8.99 },
      { name: 'Mozzarella Sticks', description: 'Breaded and fried mozzarella cheese', price: 7.99 },
      { name: 'Spinach Artichoke Dip', description: 'Creamy dip with spinach and artichokes', price: 9.99 },
    ],
    'Main Courses': [
      { name: 'Grilled Salmon', description: 'Fresh salmon with lemon butter sauce', price: 18.99 },
      { name: 'Chicken Parmesan', description: 'Breaded chicken with marinara and mozzarella', price: 16.99 },
      { name: 'Vegetable Stir Fry', description: 'Mixed vegetables in a savory sauce', price: 14.99 },
    ],
    'Desserts': [
      { name: 'Chocolate Lava Cake', description: 'Warm chocolate cake with a gooey center', price: 7.99 },
      { name: 'New York Cheesecake', description: 'Classic creamy cheesecake', price: 6.99 },
      { name: 'Fruit Tart', description: 'Pastry crust filled with custard and fresh fruits', price: 8.99 },
    ],
    'Drinks': [
      { name: 'Iced Tea', description: 'Freshly brewed and chilled', price: 2.99 },
      { name: 'Lemonade', description: 'Homemade with fresh lemons', price: 3.99 },
      { name: 'Espresso', description: 'Rich and bold coffee shot', price: 3.49 },
    ],
  };

  setActiveCategory(category: string): void {
    this.activeCategory = category;
  }
}
