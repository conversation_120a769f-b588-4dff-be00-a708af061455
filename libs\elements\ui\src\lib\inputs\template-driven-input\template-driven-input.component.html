@if(type === 'text') {
  <div class="form-control">
    <input 
      [(ngModel)]="value"
      (ngModelChange)="onValueChange($event)"
      [id]="inputId" 
      [disabled]="disabled"
      type="text" 
      (focus)="onFocus()" 
      (blur)="onBlur()" 
      [ngClass]="{
        'shadow-lg': isFocused,
        'shadow-sm': !isFocused,
        'border-primary-hoverGreen': value,
        'opacity-75 cursor-not-allowed': disabled
      }" 
      class="
        block w-full px-4 py-3 text-black
        bg-primary-cardGray border border-primary-cardGray rounded-lg 
        focus:outline-none focus:ring-1 focus:ring-primary-hoverGreen focus:border-primary-lightGreen
        transition-all duration-200 ease-in-out" 
      [placeholder]="getPlaceholder()"
      [name]="name"
    /> 
  </div>  
} @else if(type === 'selector') {

  <div class="mb-4">
    <select 
      [(ngModel)]="value"
      (ngModelChange)="onValueChange($event)"
      [disabled]="disabled"
      [id]="inputId"
      [name]="name"
      [value]="value"
      (focus)="onFocus()"
      (blur)="onBlur()"
      [ngClass]="{
        'shadow-lg': isFocused,
        'shadow-sm': !isFocused,
        'border-primary-hoverGreen': value,
        'opacity-75 cursor-not-allowed': disabled
      }"
      class="block w-full px-3 py-3 text-black bg-primary-cardGray border border-primary-cardGray 
             rounded-md shadow-sm focus:outline-none focus:ring-primary-hoverGreen focus:border-primary-hoverGreen"
    >
      <option value="">{{ getPlaceholder() }}</option>
      @for(option of options; track option.value) {
        <option [value]="option.value">{{ option.label || option }}</option>
      }
    </select>
  </div>
} @else if (type === 'password') {
  <div class="mb-4">
    <div class="relative">
      <input 
        [(ngModel)]="value"
        (ngModelChange)="onValueChange($event)"
        [disabled]="disabled"
        [type]="showPassword ? 'text' : 'password'" 
        [id]="inputId" 
        [name]="name"
        (focus)="onFocus()"
        (blur)="onBlur()"
        [ngClass]="{
          'shadow-lg': isFocused,
          'shadow-sm': !isFocused,
          'border-primary-hoverGreen': value,
          'opacity-75 cursor-not-allowed': disabled
        }"
        class="block w-full px-4 py-3 text-black bg-primary-cardGray border border-primary-cardGray 
               rounded-md shadow-sm focus:outline-none focus:ring-primary-hoverGreen focus:border-primary-hoverGreen pr-10"
        [placeholder]="getPlaceholder()" 
      />
      <button 
        [disabled]="disabled"
        type="button"
        class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500 hover:text-primary-cardGray"
        (click)="togglePasswordVisibility()"
      >
        <ng-container *ngIf="showPassword; else showIcon">
          <svg class="h-5 w-5" aria-hidden="true">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor" class="size-6">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88" />
            </svg>
          </svg>
        </ng-container>
        <ng-template #showIcon>
          <svg class="h-5 w-5" aria-hidden="true">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor" class="size-6">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
              <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
            </svg>
          </svg>
        </ng-template>
        <span class="sr-only">
          {{ showPassword ? 'Hide password' : 'Show password' }}
        </span>
      </button>
    </div>
  </div>
} @else if (type === 'number') {
  <div class="form-control">
    <input 
      [(ngModel)]="value"
      (ngModelChange)="onValueChange($event)"
      [disabled]="disabled"
      [id]="inputId" 
      type="number" 
      (focus)="onFocus()" 
      (blur)="onBlur()" 
      [ngClass]="{
        'shadow-lg': isFocused,
        'shadow-sm': !isFocused,
        'border-primary-hoverGreen': value,
        'opacity-75 cursor-not-allowed': disabled
      }" 
      class="
        block w-full px-4 py-3 text-black
        bg-primary-cardGray border border-primary-cardGray rounded-lg 
        focus:outline-none focus:ring-1 focus:ring-primary-hoverGreen focus:border-primary-lightGreen
        transition-all duration-200 ease-in-out" 
      [placeholder]="getPlaceholder()"
      [name]="name"
    /> 
  </div>  
}