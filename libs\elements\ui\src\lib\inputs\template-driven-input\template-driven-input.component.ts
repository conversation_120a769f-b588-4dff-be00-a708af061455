import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SelectOption } from '../../../models/select.model';

@Component({
  selector: 'lib-template-driven-input',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './template-driven-input.component.html',
  styleUrls: ['./template-driven-input.component.scss']
})
export class TemplateDrivenInputComponent implements OnChanges {
  @Input() inputId = '';
  @Input() value: any = '';
  @Output() valueChange = new EventEmitter<any>();
  @Input() label = '';
  @Input() type: 'text' | 'selector' | 'number' | 'date' | 'datetime' | 'time' | 'password' = "text";
  @Input() placeholder?: string;
  @Input() options: SelectOption[] = [];
  @Input() disabled = false;
  @Input() name = '';

  showPassword = false;
  isFocused = false;

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  onFocus() {
    this.isFocused = true;
  }

  onBlur() {
    this.isFocused = false;
  }

  get isFloating(): boolean {
    return this.isFocused || !!this.value;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['value']) {
      this.value = changes['value'].currentValue;
    }


  }

  getPlaceholder(): string {
    if (this.placeholder) {
      return this.placeholder;
    }

    const controlName = this.name;
    switch (controlName) {
      case 'username':
        return 'Enter your username';
      case 'email':
        return 'Enter your email';
      case 'password':
        return 'Enter your password';
      default:
        return 'Enter a value';
    }
  }



  onValueChange(newValue: any) {
    this.value = newValue;
    this.valueChange.emit(newValue);
  }
}