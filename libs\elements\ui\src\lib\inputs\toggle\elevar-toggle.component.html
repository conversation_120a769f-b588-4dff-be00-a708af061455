<div class="pb-4 pt-3">
<h1>Reusable toggle</h1>
<label class="flex items-center cursor-pointer">
  <div class="relative">
    <input
      type="checkbox"
      class="sr-only"
      [checked]="checked"
      (change)="onToggle()"
    />
    <div
      class="w-10 h-6 bg-gray-300 rounded-full shadow-inner transition-colors duration-300 ease-in-out"
      [ngClass]="{ 'bg-primary-hoverGreen': checked }"
    ></div>
    <div
      class="absolute w-4 h-4 bg-white rounded-full shadow inset-y-1 left-1 transition-transform duration-300 ease-in-out"
      [ngClass]="{ 'transform translate-x-full': checked }"
    ></div>
  </div>
  <div class="ml-3 text-gray-700 font-medium" [ngClass]="{ 'text-primary-hoverGreen': checked }">
    {{ label }}
  </div>
</label>
</div>


  <div class="flex items-center gap-8">
    <div>
      <h1>Big Toggle</h1>
      <label class="relative inline-block w-14 h-8">
        <input
          type="checkbox"
          class="sr-only peer"
          [checked]="isChecked"
          (change)="toggleSwitch()"
        />
        <span class="absolute cursor-pointer inset-0 rounded-full transition duration-300 ease-in-out bg-gray-300 peer-checked:bg-green-500
                     after:content-[''] after:absolute after:bg-white after:rounded-full after:h-6 after:w-6 after:top-1 after:left-1
                     after:transition-all after:duration-300 after:ease-in-out
                     peer-checked:after:translate-x-6 peer-active:after:w-7
                     peer-checked:peer-active:after:translate-x-5
                     after:shadow-[0_10px_40px_rgba(0,0,0,0.1)]
                     peer-checked:after:shadow-[-10px_0_40px_rgba(0,0,0,0.1)]"
        ></span>
      </label>
    </div>
    <div>
      <h2>Heart Toggle</h2>
      <div class="inline-block">
        <input type="checkbox" id="favorite" name="favorite-checkbox" value="favorite-button" class="hidden" [(ngModel)]="isChecked" (change)="toggleFavorite()">
        <label for="favorite" class="flex items-center gap-3.5 py-2.5 px-4 bg-white rounded-lg cursor-pointer select-none shadow-[0_8px_24px_rgba(149,157,165,0.2)] text-black">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" [ngClass]="{'fill-red-500 stroke-red-500 animate-heart': isChecked, 'fill-none stroke-current': !isChecked}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="transition-all duration-500">
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
          </svg>
          <div class="relative overflow-hidden grid">
            <span class="transition-all duration-500 col-start-1 col-end-1 row-start-1 row-end-1" [ngClass]="{'translate-y-0 opacity-100': !isChecked, 'translate-y-full opacity-0': isChecked}">
              Add to Favorites
            </span>
            <span class="transition-all duration-500 col-start-1 col-end-1 row-start-1 row-end-1" [ngClass]="{'translate-y-0 opacity-100': isChecked, '-translate-y-full opacity-0': !isChecked}">
              Added to Favorites
            </span>
          </div>
        </label>
      </div>
    </div>
  </div>
  