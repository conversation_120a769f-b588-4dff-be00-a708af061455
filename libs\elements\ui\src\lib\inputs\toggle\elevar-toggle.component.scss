
:host {
    display: inline-block;
  }
  
  input:checked:active + span::after {
    width: 2.25rem;
  }
  
  
  @keyframes heartButton {
    0%, 100% { transform: scale(1); }
    25%, 75% { transform: scale(1.3); }
  }
  
  .animate-heart {
    animation: heartButton 1s;
  }
  
  .peer:checked {
    background-color: var(--color, blue);
  }
  
  .peer:checked + label {
    border-color: var(--color, blue);
    transform: translateX(calc(100% - var(--knob-size)));
  }
  
  label {
    transition: transform 0.3s, border-color 0.3s;
  }
  