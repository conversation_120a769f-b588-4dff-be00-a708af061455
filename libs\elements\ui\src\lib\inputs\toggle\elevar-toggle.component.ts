import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'lib-elevar-toggle',
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './elevar-toggle.component.html',
  styleUrl: './elevar-toggle.component.scss'
})
export class ElevarToggleComponent {
  isChecked = false;
  @Input() label= 'Toggle';
  @Input() checked  = false;
  @Output() changed = new EventEmitter<boolean>();

  onToggle() {
    this.checked = !this.checked;
    this.changed.emit(this.checked);
  }


  toggleSwitch() {
    this.isChecked = !this.isChecked;
  }

  toggleFavorite() {
    this.isChecked = !this.isChecked;
  }
}
