<div *ngIf="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
      <!-- Header -->
      <div class="bg-green-600 text-white px-6 py-4 rounded-t-lg">
        <h3 class="text-xl font-semibold">{{ title }}</h3>
      </div>
  
      <!-- Body -->
      <div class="px-6 py-4">
        <ng-content select="[modal-body]"></ng-content>
      </div>
  
      <!-- Footer -->
      <div class="bg-gray-100 px-6 py-4 rounded-b-lg flex justify-end space-x-2">
        <button (click)="onCancel()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors">
          Cancel
        </button>
        <button (click)="onConfirm()" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
          Confirm
        </button>
      </div>
    </div>
  </div>
  