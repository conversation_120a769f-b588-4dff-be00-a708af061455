import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-action-modal',
  imports: [CommonModule],
  templateUrl: './action-modal.component.html',
  styleUrl: './action-modal.component.scss'
})
export class ActionModalComponent {
  @Input() isOpen = false;
  @Input() title = '';
  @Output() confirm = new EventEmitter<void>();
  // eslint-disable-next-line @angular-eslint/no-output-native
  @Output() cancel = new EventEmitter<void>();

  onConfirm() {
    this.confirm.emit();
  }

  onCancel() {
    this.cancel.emit();
  }
}

