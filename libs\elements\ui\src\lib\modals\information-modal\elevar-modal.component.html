<div *ngIf="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
  <!-- Overlay with blur effect -->
  <div class="fixed inset-0 bg-black/40 backdrop-blur-sm transition-opacity"></div>

  <!-- Modal Container -->
  <div class="flex min-h-full items-center justify-center p-4 sm:items-center sm:p-0">
    <div
      class="relative transform overflow-hidden rounded-lg bg-white shadow-2xl transition-all sm:my-8 sm:w-full sm:max-w-md">
      <!-- Modal Header -->
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold leading-6" [ngClass]="{
              'text-green-600': type === 'success',
              'text-yellow-600': type === 'warning',
              'text-red-600': type === 'error'
            }">
          {{ title }}
        </h3>
      </div>

      <!-- Modal Body -->
      <div class="px-6 py-4">
        <p class="text-sm text-gray-600 leading-relaxed">
          {{ description }}
        </p>
      </div>

      <!-- Modal Footer -->
      <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
        <button (click)="close()" type="button" [ngClass]="{
            'bg-green-600 hover:bg-green-700 focus:ring-green-500': type === 'success',
            'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500': type === 'warning',
            'bg-red-600 hover:bg-red-700 focus:ring-red-500': type === 'error'
          }"
          class="inline-flex justify-center rounded-md px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2">
          Close
        </button>

        @if(showConfirmButton){
        <button (click)="confirm()" type="button"
          class="bg-green-600 hover:bg-green-700 focus:ring-green-500 inline-flex justify-center rounded-md px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2">
          Confirm
        </button>
        }
      </div>
    </div>
  </div>
</div>