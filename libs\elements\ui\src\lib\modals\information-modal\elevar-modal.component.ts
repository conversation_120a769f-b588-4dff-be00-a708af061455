import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-elevar-modal',
  imports: [CommonModule],
  templateUrl: './elevar-modal.component.html',
  styleUrl: './elevar-modal.component.scss'
})
export class ElevarModalComponent {
  @Input() isOpen = false;
  @Input() title = 'Error';
  @Input() description = 'An error occurred.';
  @Input() type: 'success' | 'error' | 'warning' = 'success';
  @Input() showConfirmButton = false;

  @Output() confirmButtonClicked = new EventEmitter<boolean>();


  close() {
    this.isOpen = false;
  }

  confirm(){
    this.confirmButtonClicked.emit(true);
    this.isOpen = false;
  }
}
