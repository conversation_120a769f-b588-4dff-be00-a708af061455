<div class="flex flex-row justify-between px-12 py-3 items-center">
    <div class="text-black flex items-center flex-col font-extrabold">
      <span class="text-2xl">Elevar</span>
    </div>
    <ul class="hidden sm:flex flex-row justify-between items-center gap-6 font-semibold text-sm">
      <li *ngFor="let item of itemArray; index as i" class="hover:text-primary-hoverGreen duration-200">
        <a href="">{{ item.title }}</a>
      </li>
      <li>
        <button
          class="bg-primary-lightGreen duration-200 p-2 rounded-full text-white hover:border hover:bg-white hover:border-primary-hoverGreen hover:text-primary-hoverGreen"
        >
          Order Now
        </button>
      </li>
    </ul>
    <button (click)="toggleMenu()" class="sm:hidden">
      <ng-container *ngIf="isOpen">
        <i class="text-2xl text-primary-lightGreen">✖</i>
      </ng-container>
      <ng-container *ngIf="!isOpen">
        <i class="text-2xl text-primary-lightGreen">☰</i>
      </ng-container>
    </button>
  </div>
  
<div>
    <ul
      class="flex sm:hidden flex-col items-center gap-4 font-semibold text-sm duration-700 overflow-hidden transition-all"
      [class.h-56]="isOpen"
      [class.h-0]="!isOpen"
    >
      <li *ngFor="let item of itemArray; index as i" class="hover:text-primary-hoverGreen duration-200">
        <a href="">{{ item.title }}</a>
      </li>
      <li>
        <button
          class="bg-primary-lightGreen duration-200 p-2 rounded-full text-white hover:border hover:bg-white hover:border-primary-hoverGreen hover:text-primary-hoverGreen"
        >
          Order Now
        </button>
      </li>
    </ul>
</div>

  
  