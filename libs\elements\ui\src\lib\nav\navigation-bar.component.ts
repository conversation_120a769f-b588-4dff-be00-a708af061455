import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-navigation-bar',
  imports: [CommonModule],
  templateUrl: './navigation-bar.component.html',
  styleUrl: './navigation-bar.component.scss'
})
export class NavigationBarComponent {
  isOpen = false;
  itemArray = [
    { title: 'Home' },
    { title: 'About' },
    { title: 'Services' },
    { title: 'Contact' },
  ];

  toggleMenu(): void {
    this.isOpen = !this.isOpen;
  }
}
