<header class="bg-primary-lightGreen text-white p-2 py-3" [class.p-4]="!isSearchable">
  <div [class]="showArrowIcon ? 'flex items-center justify-start gap-2 mb-2' : 'mb-2'">
    <button class="flex items-center" (click)="goBack()" *ngIf="showArrowIcon">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M19 12H5M12 19l-7-7 7-7" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
    <h1 class="text-xl font-semibold" [class.text-center]="!showArrowIcon" [class.w-full]="!showArrowIcon">
      {{ header | uppercase }}
    </h1>
  </div>

  @if(isSearchable){
    <lib-search-input [placeholder]="placeholder" (search)="search.emit($event)"/>
  }

</header>
