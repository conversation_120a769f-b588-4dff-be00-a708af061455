import { Component, EventEmitter, inject, Input, Output, signal } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { Router } from '@angular/router';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { POSInvoice } from '@models';

import { SearchInputComponent } from '../search-input/search-input.component';

@Component({
  selector: 'lib-page-header',
  imports: [CommonModule, FormsModule, ReactiveFormsModule, SearchInputComponent],
  templateUrl: './page-header.component.html',
  styleUrl: './page-header.component.scss'
})
export class PageHeaderComponent {
  @Input() header!: string;
  @Input() showArrowIcon!: boolean;
  @Input() isSearchable!: boolean;
  @Input() customBackUrl!: string;
  @Input() placeholder = 'Search...';
  @Input() invoices!: POSInvoice[];
  @Output() filteredData = new EventEmitter<POSInvoice[]>;
  @Output() searchString = new EventEmitter<string>;
  @Output() performActionOnBack: EventEmitter<boolean> = new EventEmitter();
  @Output() search = new EventEmitter<string>;

  previousUrl!: string;
  private readonly route = inject(Router);
  private readonly _location = inject(Location);

  searchTerm = signal('');

  constructor(private readonly router: Router) {
    const navigation = this.router.getCurrentNavigation();
    if (navigation?.previousNavigation?.finalUrl) {
      this.previousUrl = navigation.previousNavigation.finalUrl.toString();
    }
  }

  formGroup = new FormGroup({
    search: new FormControl('', [Validators.required]),
  });

  get searchControl(): FormControl {
    return this.formGroup.get('search') as FormControl;
  }

  isSelector(formControl: string) {
    return formControl.includes('selected');
  }

  isPassword(formControl: string) {
    return formControl.includes('password');
  }

  goBack() {
    if (this.customBackUrl) {
      this.router.navigate([this.customBackUrl]);
      this.performActionOnBack.emit(true);
      return;
    }

    if (this.previousUrl) {
      this.route.navigate([this.previousUrl]);
    }
    else {
      this._location.back();
    }
  }

  emitData(value: any) {
    this.filteredData.emit(value);
  }

  stringEmit(value: any) {
    this.search.emit(value);
  }
}
