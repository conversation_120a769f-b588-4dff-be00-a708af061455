<div class="bg-gray-50 rounded-xl p-4 flex items-end justify-between">
  <div class="space-y-2">
    <h3 class="text-lg font-medium text-gray-800">{{ product.item_name ? product.item_name : product.item_code }}</h3>
    <div class="flex items-center space-x-4">
      <button
        class="w-8 h-8 flex items-center justify-center bg-white rounded-full border border-gray-200 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
        [disabled]="isDecrementDisabled"
        (click)="onDecrement()">
        −
      </button>
      <span class="text-gray-800">{{ product.quantity | number:'1.3-3' }}</span>
      <button
        class="w-8 h-8 flex items-center justify-center bg-white rounded-full border border-gray-200 text-gray-600"
        (click)="onIncrement()">
        +
      </button>
      <span class="text-gray-600">{{ product.stock_uom }}</span>
    </div>
  </div>
  <div class="flex items-end space-x-4">
    <span class="text-lg font-medium">KES {{ totalPrice }}</span>
    <button class="text-red-500" (click)="onRemove()">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
      </svg>
    </button>
  </div>
</div>
