import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CartItem } from '@models';
import { roundUpToShilling } from '@core';

@Component({
  selector: 'lib-product-quantity-list',
  imports: [CommonModule],
  templateUrl: './product-quantity-list.component.html',
  styleUrl: './product-quantity-list.component.scss'
})
export class ProductQuantityListComponent {
  @Input() product!: CartItem
  @Output() increment = new EventEmitter<void>();
  @Output() decrement = new EventEmitter<void>();
  @Output() remove = new EventEmitter<void>();

  get totalPrice(): number {
    const unitPrice = this.product?.price ?? 0;
    const quantity = this.product?.quantity ?? 0;
    const rawTotal = unitPrice * quantity;
    return roundUpToShilling(rawTotal); // Round up to nearest shilling
  }

  get isDecrementDisabled(): boolean {
    const quantity = this.product?.quantity ?? 0;
    return quantity <= 0.001; // Disable decrement if quantity would go below minimum
  }

  onIncrement() {
    this.increment.emit();
  }

  onDecrement() {
    // Only allow decrement if quantity would remain above minimum
    if (!this.isDecrementDisabled) {
      this.decrement.emit();
    }
  }

  onRemove() {
    this.remove.emit();
  }
}
