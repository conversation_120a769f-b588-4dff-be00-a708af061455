.loader {
    display: block;
    --height-of-loader: 4px;
    --loader-color: #60a779;
    width: 130px;
    height: var(--height-of-loader);
    border-radius: 30px;
    background-color: rgba(0,0,0,0.2);
    position: relative;
  }
  
  .loader::before {
    content: "";
    position: absolute;
    background: var(--loader-color);
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    border-radius: 30px;
    animation: moving 1s ease-in-out infinite;
    ;
  }
  
  @keyframes moving {
    50% {
      width: 100%;
    }
  
    100% {
      width: 0;
      right: 0;
      left: unset;
    }
  }


.loader-2 {
    width: 80px;
    height: 50px;
    position: relative;
  }
  
  .loader-text {
    position: absolute;
    top: 0;
    padding: 0;
    margin: 0;
    color: #8dc63f;
    animation: text_713 3.5s ease both infinite;
    font-size: .8rem;
    letter-spacing: 1px;
  }
  
  .load {
    background-color: #72a230;
    border-radius: 50px;
    display: block;
    height: 16px;
    width: 16px;
    bottom: 0;
    position: absolute;
    transform: translateX(64px);
    animation: loading_713 3.5s ease both infinite;
  }
  
  .load::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    background-color: #60a779;
    border-radius: inherit;
    animation: loading2_713 3.5s ease both infinite;
  }
  
  @keyframes text_713 {
    0% {
      letter-spacing: 1px;
      transform: translateX(0px);
    }
  
    40% {
      letter-spacing: 2px;
      transform: translateX(26px);
    }
  
    80% {
      letter-spacing: 1px;
      transform: translateX(32px);
    }
  
    90% {
      letter-spacing: 2px;
      transform: translateX(0px);
    }
  
    100% {
      letter-spacing: 1px;
      transform: translateX(0px);
    }
  }
  
  @keyframes loading_713 {
    0% {
      width: 16px;
      transform: translateX(0px);
    }
  
    40% {
      width: 100%;
      transform: translateX(0px);
    }
  
    80% {
      width: 16px;
      transform: translateX(64px);
    }
  
    90% {
      width: 100%;
      transform: translateX(0px);
    }
  
    100% {
      width: 16px;
      transform: translateX(0px);
    }
  }
  
  @keyframes loading2_713 {
    0% {
      transform: translateX(0px);
      width: 16px;
    }
  
    40% {
      transform: translateX(0%);
      width: 80%;
    }
  
    80% {
      width: 100%;
      transform: translateX(0px);
    }
  
    90% {
      width: 80%;
      transform: translateX(15px);
    }
  
    100% {
      transform: translateX(0px);
      width: 16px;
    }
  }
   
   
  
  