<div class="p-4">
  <div class="relative">
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5"
      fill="none" 
      viewBox="0 0 24 24" 
      stroke="currentColor"
    >
      <path 
        stroke-linecap="round" 
        stroke-linejoin="round" 
        stroke-width="2" 
        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
      />
    </svg>
    <input 
      type="text"
      aria-label="Search"
      [placeholder]="placeholder"
      [(ngModel)]="searchValue"
      class="w-full pl-10 py-2 bg-gray-50 text-black rounded-md focus:outline-none focus:ring-2 focus:ring-primary-hoverGreen focus:ring-opacity-50"
      (ngModelChange)="onSearch($event)"
      >
  </div>
</div>