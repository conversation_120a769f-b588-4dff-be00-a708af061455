import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'lib-search-input',
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './search-input.component.html',
  styleUrl: './search-input.component.scss'
})
export class SearchInputComponent {
  @Input() placeholder = 'Search...';
  type= "invoices"
  searchValue: string = '';
  @Output() search: EventEmitter<string> = new EventEmitter<string>();

  onSearch(value: string): void {
    this.search.emit(value.trim());
  }
}