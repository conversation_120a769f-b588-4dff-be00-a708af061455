import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

interface ModalState {
  isOpen: boolean;
  title: string;
  description: string;
  type: 'success' | 'error';
}

@Injectable({
  providedIn: 'root'
})
export class DialogService {
    private isOpenSubject = new BehaviorSubject<boolean>(false);
  isOpen$ = this.isOpenSubject.asObservable();
  private modalState = new BehaviorSubject<ModalState>({
    isOpen: false,
    title: '',
    description: '',
    type: 'success'
  });

  modalState$ = this.modalState.asObservable();

  openModal(title: string, description: string, type: 'success' | 'error') {
    this.modalState.next({ isOpen: true, title, description, type });
  }

  closeModal() {
    this.modalState.next({ ...this.modalState.value, isOpen: false });
  }

  open() {
    this.isOpenSubject.next(true);
  }

  close() {
    this.isOpenSubject.next(false);
  }

}

