import { Injectable } from '@angular/core';
import { encryptObject, decryptObject, ENCRYPTION_KEY } from '@core';

@Injectable({
  providedIn: 'root'
})
export class FooterStateService {
  private readonly activeButtonKey = 'activeButton';

  saveActiveButton(button: string) {
    localStorage.setItem(this.activeButtonKey, encryptObject(button, ENCRYPTION_KEY));
  }

  getActiveButton(): string | null {
    const value = localStorage.getItem(this.activeButtonKey);
    return value ? decryptObject(value, ENCRYPTION_KEY) : null;
  }
}
