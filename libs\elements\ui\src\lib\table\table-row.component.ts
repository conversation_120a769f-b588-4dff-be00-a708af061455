import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { User } from '../../models/user.model';
import { FormsModule } from '@angular/forms';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: '[app-table-row]',
  imports: [CommonModule, FormsModule],
  templateUrl: './table-row.component.html',
  styleUrl: './table-row.component.scss'
})
export class TableRowComponent {
  @Input() user: User = <User>{};

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  constructor() {}


}
