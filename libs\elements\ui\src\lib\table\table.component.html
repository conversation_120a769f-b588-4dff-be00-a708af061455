<div class="mb-4 flex justify-between pt-20">
    <div class="inline-block">
      <h3 class="font-semibold text-primary-hoverGreen">Team Members</h3>
      <div class="space-x-1 text-xs font-medium text-primary-lightGreen">
        <a href="" class="hover:text-primary-light">All Members:</a>
        <span class="text-primary-lightGreen">49,053</span>
      </div>
    </div>
    <div class="inline-block space-x-4">
      <button
        class="flex-none rounded-md bg-primary-lightGreen px-4 py-2.5 text-xs font-semibold text-muted-foreground hover:text-primary-hoverGreen">
        Import CSV
      </button>
      <button class="flex-none rounded-md bg-primary-lightGreen px-4 py-2.5 text-xs font-semibold hover:text-primary-hoverGreen">
        Add Member
      </button>
    </div>
  </div>
  
  <div class="flex min-w-full flex-col rounded-xl border border-primary-lightGreen bg-primary-white p-2">
     <lib-table-action></lib-table-action>
    <div
      class="scrollbar-thumb-rounded scrollbar-track-rounded grow overflow-x-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-muted">
      <table
        class="table w-full table-auto border-collapse border-0 text-left align-middle leading-5 text-muted-foreground">
        <thead class="border border-muted/20 text-xs text-muted-foreground">
          <tr app-table-header (onCheck)="toggleUsers($event)"></tr>
        </thead>
        <tbody>
          <tr
          class="hover:bg-card/50"
          *ngFor="let user of filteredUsers(); let i = index"
          app-table-row
          [user]="user"
        ></tr>
        </tbody>
      </table>
    </div>
    <lib-table-footer></lib-table-footer>
  </div>
  