// toast.component.ts
import { Component, Input, Output, EventEmitter, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  toast,
  NgxSonnerToaster,
  ToastT,
  Position,
  ToastTypes,
  PromiseT,
  Theme
} from 'ngx-sonner';

@Component({
  selector: 'lib-toast',
  standalone: true,
  imports: [CommonModule, NgxSonnerToaster],
  templateUrl: './toast.component.html',
  encapsulation: ViewEncapsulation.None
})
export class ToastComponent {
  @Input() theme: Theme = 'light';
  @Input() position: Position = 'top-center';
  @Input() richColors = true;
  @Input() expand = false;
  @Input() duration = 4000;
  @Input() visibleToasts = 3;
  @Input() closeButton = false;
  @Input() offset: string | number = '1';
  @Input() gap = 14;
  @Input() className?: string;
  @Input() descriptionClass?: string;

  @Output() toastDismissed = new EventEmitter<ToastT>();
  @Output() toastAutoClosed = new EventEmitter<ToastT>();
  @Output() actionClicked = new EventEmitter<MouseEvent>();

  protected readonly toast = toast;

  showToast(
    message: string,
    options: Partial<ToastT> = {}
  ) {
    const toastOptions: Partial<ToastT> = {
      title: message,
      duration: this.duration,
      class: this.className,
      descriptionClass: this.descriptionClass,
      position: this.position,
      closeButton: this.closeButton,
      onDismiss: (toast) => {
        this.toastDismissed.emit(toast);
      },
      onAutoClose: (toast) => {
        this.toastAutoClosed.emit(toast);
      },
      ...options
    };

    return toast(message, toastOptions);
  }

  showTypedToast(type: Exclude<ToastTypes, 'action' | 'default'>, message: string, options: Partial<ToastT> = {}) {
    const toastFn = toast[type] as (message: string, options?: Partial<ToastT>) => string | number;
    return toastFn(message, options);
  }

  success(message: string, options: Partial<ToastT> = {}) {
    return this.showTypedToast('success', message, options);
  }

  error(message: string, options: Partial<ToastT> = {}) {
    return this.showTypedToast('error', message, options);
  }

  warning(message: string, options: Partial<ToastT> = {}) {
    return this.showTypedToast('warning', message, options);
  }

  info(message: string, options: Partial<ToastT> = {}) {
    return this.showTypedToast('info', message, options);
  }

  loading(message: string, options: Partial<ToastT> = {}) {
    return this.showTypedToast('loading', message, options);
  }

  async promise<T>(
    promise: PromiseT<T>,
    options: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: unknown) => string);
      finally?: () => void | Promise<void>;
    }
  ) {
    return toast.promise(promise, options);
  }

  showActionToast(
    message: string,
    action: {
      label: string;
      onClick: (event: MouseEvent) => void;
    },
    options: Partial<ToastT> = {}
  ) {
    return this.showToast(message, {
      ...options,
      action: {
        label: action.label,
        onClick: (event) => {
          action.onClick(event);
          this.actionClicked.emit(event);
        }
      }
    });
  }

  dismiss(toastId: number | string) {
    toast.dismiss(toastId);
  }
}