{"name": "auth", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/features/auth/src", "prefix": "auth", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/features/auth/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/features/auth/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/features/auth/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/features/auth/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}