<h1>Menu Bar</h1>
<div class=" bg-gray-100 py-8">
    <lib-elevar-menu-tab></lib-elevar-menu-tab>
</div>


<div class="container mx-auto p-4">
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
        <div>
            <lib-button>Default</lib-button>
            <lib-button buttonType="secondary-rounded">Alternative</lib-button>
        </div>
        <div>
            <lib-button buttonType="success-btn">Success</lib-button>
            <lib-button buttonType="notify-blue">Notify Blue</lib-button>

        </div>

        <div>
            <lib-button buttonType="disabled">Disabled But<PERSON></lib-button>
            <lib-button buttonType="loading">Loading Button</lib-button>
        </div>
    </div>
</div>


<div class="m-4">
    <lib-product-card></lib-product-card>
    <lib-inof-card></lib-inof-card>
    <lib-summary-card></lib-summary-card>
    <!-- <lib-hover-cards></lib-hover-cards> -->
    <lib-interactive-card></lib-interactive-card>
    <lib-navigation-bar></lib-navigation-bar>
    <lib-footer></lib-footer>
    <lib-table></lib-table>
    <lib-toast></lib-toast>

    <lib-circular-progress class="w-full"></lib-circular-progress>
    <lib-line-progress class="w-full"></lib-line-progress>

    <div class="p-4">
        <h1 class="text-2xl font-bold mb-4">Angular Feedback Modal Example</h1>
        <div class="space-x-4">
            <button (click)="showSuccess()"
                class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50">
                Show Success Modal
            </button>
            <button (click)="showError()"
                class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50">
                Show Error Modal
            </button>
        </div>
        <lib-elevar-modal [isOpen]="modalState.isOpen" [title]="modalState.title" [description]="modalState.description"
            [type]="modalState.type"></lib-elevar-modal>
    </div>
</div>

<div class="m-4">
    <h3>Inputs</h3>
    <lib-elevar-inputs [control]="formGroup.controls.userName" [inputId]='"userName"' 
    [label]="'userName'" [type]="isPassword('userName') ? 'password' : isSelector('userName') ? 'selector' : 'text'" >
    </lib-elevar-inputs>

    <lib-elevar-inputs [control]="formGroup.controls.selectedRegion" [inputId]='"selectedRegion"' [label]="'selectedRegion'" 
    [type]="isPassword('selectedRegion') ? 'password' : isSelector('selectedRegion') ? 'selector' : 'text'">
    </lib-elevar-inputs>

    <lib-elevar-inputs [control]="formGroup.controls.password" [inputId]='"password"' [label]="'password'" 
    [type]="isPassword('password') ? 'password' : isSelector('password') ? 'selector' : 'text'">
    </lib-elevar-inputs>
</div>


<div class="m-4">
    <lib-elevar-checkbox [label]="'Remember me'" [checked]="checked" (change)="onChanged($event)"></lib-elevar-checkbox>
    <lib-elevar-toggle label="Enable feature"
    [checked]="isFeatureEnabled"
    (changed)="onFeatureToggle($event)"></lib-elevar-toggle>


</div>

<div class="m-4">
<lib-elevar-radio
  [options]="radioOptions"
  name="framework"
  [selectedValue]="selectedFramework"
  (selectionChange)="onFrameworkChange($event)"
></lib-elevar-radio>
</div>