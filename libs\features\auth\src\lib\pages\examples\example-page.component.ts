import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent, CircularProgressComponent, DialogService, ElevarModalComponent, FooterComponent, ElevarInputsComponent, HoverCardsComponent, InofCardComponent, InteractiveCardComponent, LineProgressComponent, NavigationBarComponent, ProductCardComponent, SummaryCardComponent, TableComponent, ToastComponent, ElevarCheckboxComponent, ElevarToggleComponent, ElevarHarmburgerComponent, ElevarRadioComponent, ElevarMenuTabComponent } from '@elements/ui';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';

@Component({
  selector: 'auth-example-page',
  imports: [CommonModule, ButtonComponent, ElevarInputsComponent, ElevarRadioComponent, ElevarToggleComponent, ElevarCheckboxComponent, ToastComponent, LineProgressComponent, CircularProgressComponent, ElevarModalComponent, TableComponent, ProductCardComponent, FooterComponent, NavigationBarComponent, InofCardComponent, SummaryCardComponent, InteractiveCardComponent, ElevarInputsComponent, ReactiveFormsModule, FormsModule, ElevarMenuTabComponent],
  templateUrl: './example-page.component.html',
  styleUrl: './example-page.component.scss'
})
export class ExamplePageComponent implements OnInit{
  showModal = false;
  isFeatureEnabled = false;
  isModalOpen$: any;

  checked = false;

  radioOptions = [
    { label: 'HTML', value: 'html' },
    { label: 'React', value: 'react' },
  ];
  
  selectedFramework = 'react';

  // FormGroup
  formGroup = new FormGroup({
    userName: new FormControl('', [Validators.required]),
    email: new FormControl('', [Validators.required]),
    selectedRegion: new FormControl('', [Validators.required]),
    password: new FormControl(''),
    toggleInput: new FormControl(''),

  });

  modalState: {isOpen: boolean; title: string; description: string; type: 'success' | 'error'} = {
    isOpen: false,
    title: '',
    description: '',
    type: 'success'
  };

  constructor(private feedbackModalService: DialogService) {
    this.feedbackModalService.modalState$.subscribe(state => {
      this.modalState = state;
    });

    this.isModalOpen$ = this.feedbackModalService.isOpen$;
  }

  ngOnInit(): void {
      this.formGroup.valueChanges
           .subscribe((val) => {
            console.log('Changed Values', val);
           })
  }

  showSuccess() {
    this.feedbackModalService.openModal('Success!', 'Your action was completed successfully.', 'success');
  }

  showError() {
    this.feedbackModalService.openModal('Error Occurred', 'An unexpected error has occurred. Please try again later.', 'error');
  }

  onFrameworkChange(selected: string) {
    console.log('Selected Framework:', selected);
  }

  onToggle(color: string, state: boolean): void {
    console.log(`${color} toggle is now ${state ? 'on' : 'off'}`);
  }

  handleModalAction(result: boolean): void {
    this.showModal = false;
    if (result) {
      console.log('Confirmed!');
    } else {
      console.log('Cancelled!');
    }
  }

  openModal() {
    this.feedbackModalService.open();
  }

  onConfirm() {
    console.log('Confirmed');
    this.feedbackModalService.close();
  }

  onCancel() {
    console.log('Cancelled');
    this.feedbackModalService.close();
  }

  isSelector(formControl: string){
    return formControl.includes('selected');
  }

  isPassword(formControl: string){
    return formControl.includes('password');
  }

  onChanged(event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;
    this.checked = isChecked;
  }

  onFeatureToggle(checked: boolean) {
    this.isFeatureEnabled = checked;
    console.log('Feature is now', checked ? 'enabled' : 'disabled');
  }

}
