@if (login$ | async) {}

@if (isLoading) {
  <lib-loader></lib-loader>
}

<lib-toast
  #loginToast
  position="top-right"
  [richColors]="true"
  [duration]="4000"
  [closeButton]="true"
  (toastDismissed)="onToastDismissed()"
/>

<div class="h-screen bg-white flex items-center justify-center p-5">
  <div class="w-full max-w-md text-center">
    <!-- Logo -->
    <div class="w-full flex justify-center items-center -mt-8 mb-8">
      <img src="/icons/logo-2.svg" alt="Logo" class="w-64 h-32" />
    </div>

    <div class="py-5">
      <p class="text-2xl font-bold py-2">WELCOME BACK</p>
      <p class="text-gray-600 text-sm font-light mb-8">
        Sign in to continue to your account
      </p>
    </div>

    <div class="space-y-4">
      <button
        (click)="loginWithERPNext()"
        class="w-full flex items-center justify-center gap-3 bg-primary hover:bg-primary-dark text-white font-medium py-3 px-4 rounded-md transition-colors"
        [disabled]="isLoading"
      >
        @if (isLoading) {
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        }
        Sign in with ERPNext
      </button>
    </div>
  </div>
</div>
