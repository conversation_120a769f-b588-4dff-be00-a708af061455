import { Component, inject, ViewChild, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Observable } from 'rxjs';
import { LoaderComponent, ToastComponent } from '@elements/ui';
import { AuthService } from '@elevar-clients/api';

@Component({
  selector: 'auth-login',
  standalone: true,
  imports: [
    CommonModule,
    LoaderComponent,
    ToastComponent
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
})
export class LoginComponent implements OnInit {
  @ViewChild('loginToast') toastComponent!: ToastComponent;

  private readonly _authService = inject(AuthService);

  login$ = new Observable<void>();
  isLoading = false;

  ngOnInit(): void {
    // Check if we're coming back from OAuth callback
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');

    if (code && state) {
      // this.handleOAuthCallback(code, state);
    }
  }

  async loginWithERPNext(): Promise<void> {
    try {
      this.isLoading = true;
      await this._authService.login();
      // The login method will handle the redirect to ERPNext
      // The rest of the flow will be handled by the OAuth callback
    } catch (error) {
      this.isLoading = false;
      console.error('Login error:', error);
      this.toastComponent.error('Login Error', {
        description: 'An error occurred during login. Please try again.',
        duration: 5000
      });
    }
  }


  onToastDismissed() {
    console.log('Toast dismissed');
  }
}
