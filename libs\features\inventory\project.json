{"name": "inventory", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/features/inventory/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/features/inventory/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/features/inventory/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/features/inventory/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/features/inventory/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}