<div class="flex flex-col h-[calc(100vh-80px)] bg-gray-50">
  <lib-page-header [header]="'Inventory Management'" [showArrowIcon]="false" [isSearchable]="false" />
  <main class="flex-1 flex items-center justify-center p-6 pt-0 max-h-[calc(100vh-255px)]">
    <div class="grid grid-cols-2 gap-6 max-w-md w-full">
      @for(item of menuItems; track item.title){
      <button
        class="bg-primary-lightGreen hover:bg-primary-hoverGreen text-white h-36 flex flex-col items-center justify-center space-y-3 rounded-lg transition-colors duration-200 shadow-md hover:shadow-lg"
        (click)="item.action()">
        <div class="flex items-center justify-center w-12 h-12">
          <span [innerHTML]="item.icon"></span>
        </div>
        <span class="font-medium text-sm">{{item.title}}</span>
      </button>
      }
    </div>
  </main>
  <lib-page-footer />
</div>
