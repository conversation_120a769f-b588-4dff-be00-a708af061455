.inventory-container {
    min-height: 100vh;
    background-color: white;
  
    header {
      background-color: #8AB86E;
      padding: 1.5rem;
  
      h1 {
        color: white;
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
      }
    }
  
    main {
      padding: 1.5rem;
    }
  }
  
  img.icon {
    filter: invert(1);
  }
  
  .grid-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    max-width: 32rem;
    margin: 0 auto;
  }
  
  .menu-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #8AB86E;
    color: white;
    border: none;
    border-radius: 0.5rem;
    height: 8rem;
    padding: 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
  
    &:hover {
      background-color: #7AA55D;
    }
  
    mat-icon {
      font-size: 2rem;
      height: 2rem;
      width: 2rem;
      margin-bottom: 0.5rem;
    }
  
    span {
      font-weight: 500;
    }
  }