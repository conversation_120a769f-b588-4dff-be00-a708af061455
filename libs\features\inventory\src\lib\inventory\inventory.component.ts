import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { PageFooterComponent, PageHeaderComponent } from '@elements/ui';

@Component({
  selector: 'lib-inventory',
  imports: [CommonModule, PageFooterComponent, PageHeaderComponent],
  templateUrl: './inventory.component.html',
  styleUrl: './inventory.component.scss',
})
export class InventoryComponent {
  router = inject(Router);

  menuItems = [
    {
      title: 'STOCK',
      icon: `<img src="/icons/cherry-2.png" alt="Stock" class="w-10 h-10 object-contain" />`,
      action: () => this.router.navigate(['/inventory/stock-list'])
    },
    {
      title: 'RECONCILE',
      icon: `<img src="/icons/scale.png" alt="Reconcile" class="w-10 h-10 object-contain" />`,
      action: () => this.router.navigate(['/inventory/stock-reconciliation'])
    },
    {
      title: 'REQUEST',
      icon: `<img src="/icons/basket.png" alt="Request" class="w-10 h-10 object-contain" />`,
      action: () => this.router.navigate(['/inventory/material-request'])
    },
    {
      title: 'RECEIVE',
      icon: `<img src="/icons/bricks.png" alt="Receive" class="w-10 h-10 object-contain" />`,
      action: () => this.router.navigate(['/inventory/material-transfer'])
    }
  ];
}
