import { Component, inject, On<PERSON><PERSON>roy, ViewChild, ElementRef, AfterViewInit, Signal, WritableSignal, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { SharedModule } from '@elements/ui';
import { MaterialRequestService, PosProfileService } from '@elevar-clients/api';
import { MaterialRequest } from '@models';
import { lastValueFrom, Subject } from 'rxjs';
import { LoaderComponent } from '@elements/ui';

@Component({
  selector: 'lib-material-request',
  imports: [CommonModule, SharedModule, LoaderComponent],
  templateUrl: './material-request.component.html',
  styleUrl: './material-request.component.scss'
})
export class MaterialRequestComponent implements OnDestroy, AfterViewInit {
  @ViewChild('scrollContainer', { static: false }) scrollContainer!: ElementRef;

  private readonly router = inject(Router);
  private readonly materialRequestService = inject(MaterialRequestService);
  private readonly posProfileService = inject(PosProfileService);
  private readonly destroy$ = new Subject<void>();

  header = 'Material Request';
  private readonly PAGE_SIZE = 20;

  requests: WritableSignal<MaterialRequest[]> = signal([]);
  isLoading = signal(false);
  isLoadingMore = signal(false);
  allLoaded = signal(false);
  currentRange = signal({ start: 0, end: 0, total: 0 });
  searchQuery = signal('');

  // Computed for filtered requests (search)
  filteredRequests = computed(() => {
    const query = this.searchQuery().toLowerCase();
    const list = this.requests();
    if (!query) return list;
    return list.filter(item =>
      Object.values(item).some(val =>
        typeof val === 'string' && val.toLowerCase().includes(query)
      )
    );
  });

  constructor() {
    this.loadInitialRequests();
  }

  ngAfterViewInit() {
    this.setupScrollListener();
  }

  private setupScrollListener() {
    if (this.scrollContainer) {
      const element = this.scrollContainer.nativeElement;
      element.addEventListener('scroll', () => {
        this.handleScroll();
      });
    }
  }

  private handleScroll() {
    if (this.scrollContainer) {
      const element = this.scrollContainer.nativeElement;
      const { scrollTop, scrollHeight, clientHeight } = element;
      // Load more when user is near the bottom (within 100px)
      if (scrollHeight - scrollTop - clientHeight < 100 && !this.isLoadingMore() && !this.allLoaded()) {
        this.loadMoreRequests();
      }
    }
  }

  async loadInitialRequests() {
    this.isLoading.set(true);
    this.allLoaded.set(false);
    this.requests.set([]);
    this.currentRange.set({ start: 0, end: 0, total: 0 });
    this.searchQuery.set('');
    const profile = this.posProfileService.posProfile();
    if (!profile?.company) {
      this.isLoading.set(false);
      return;
    }
    try {
      const response = await lastValueFrom(this.materialRequestService.getMaterialRequestsByCompany(0, this.PAGE_SIZE));
      const data = response && response.data ? response.data : [];
      this.requests.set(data);
      this.currentRange.set({ start: 1, end: data.length, total: data.length });
      if (data.length < this.PAGE_SIZE) {
        this.allLoaded.set(true);
      }
    } catch (e) {
      this.requests.set([]);
      this.allLoaded.set(true);
    } finally {
      this.isLoading.set(false);
    }
  }

  async loadMoreRequests() {
    if (this.isLoadingMore() || this.allLoaded()) return;
    this.isLoadingMore.set(true);
    const currentCount = this.requests().length;
    const profile = this.posProfileService.posProfile();
    if (!profile?.company) {
      this.isLoadingMore.set(false);
      return;
    }
    try {
      const response = await lastValueFrom(this.materialRequestService.getMaterialRequestsByCompany(currentCount, this.PAGE_SIZE));
      const newData = response && response.data ? response.data : [];
      if (newData.length > 0) {
        const updated = [...this.requests(), ...newData];
        this.requests.set(updated);
        this.currentRange.set({ start: 1, end: updated.length, total: updated.length });
        if (newData.length < this.PAGE_SIZE) {
          this.allLoaded.set(true);
        }
      } else {
        this.allLoaded.set(true);
      }
    } catch (e) {
      this.allLoaded.set(true);
    } finally {
      this.isLoadingMore.set(false);
    }
  }

  onSearchUpdated(query: string) {
    this.searchQuery.set(query.trim());
  }

  navigateToNewRequest() {
    this.router.navigate(['/inventory/new-material-request']);
  }

  navigatetoMaterialRequestDetails(name: string) {
    this.router.navigateByUrl(`/inventory/request-details/${name}`);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
