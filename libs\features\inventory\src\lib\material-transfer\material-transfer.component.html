<lib-page-header [header]="header" [showArrowIcon]="true" [isSearchable]="true" [placeholder]="'Search Material Transfer'" (search)="onSearchUpdated($event)"/>
<div class="flex flex-col h-[calc(100vh-80px)] bg-gray-50">
  <!-- Range Display -->
  @if (filteredEntries().length > 0) {
    <div class="px-4 py-2 bg-gray-100 border-b border-gray-200">
      <p class="text-sm text-gray-600">
        Showing {{ currentRange().start }} - {{ currentRange().end }} of material transfers
      </p>
    </div>
  }
  <main #scrollContainer class="flex-1 overflow-auto p-4 space-y-4 max-h-[calc(100vh-255px)]">
    <lib-loader *ngIf="isLoading()"></lib-loader>
    @for(entry of filteredEntries(); track entry.name) {
      <lib-product-card [document]="entry" [type]="'material-transfer'" (click)="navigateToDetails(entry.name)"/>
    } @empty {
      <div class="text-center text-gray-500 p-4">
        No material transfers found
      </div>
    }
    <lib-loader *ngIf="isLoadingMore() && !allLoaded()"></lib-loader>
    <!-- End of List Message -->
    @if (allLoaded() && filteredEntries().length > 0) {
      <div class="flex justify-center py-4">
        <p class="text-sm text-gray-500">You've reached the end of the list. ✨</p>
      </div>
    }
  </main>
  <lib-page-footer />
</div>
