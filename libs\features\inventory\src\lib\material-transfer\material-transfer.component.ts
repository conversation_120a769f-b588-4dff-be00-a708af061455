import { Component, inject, On<PERSON><PERSON>roy, ViewChild, ElementRef, AfterViewInit, Signal, WritableSignal, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { SharedModule } from '@elements/ui';
import { MaterialTransferService, PosProfileService } from '@elevar-clients/api';
import { lastValueFrom, Subject } from 'rxjs';
import { LoaderComponent } from '@elements/ui';

@Component({
  selector: 'lib-material-transfer',
  imports: [CommonModule, SharedModule, LoaderComponent],
  templateUrl: './material-transfer.component.html',
  styleUrl: './material-transfer.component.scss'
})
export class MaterialTransferComponent implements OnDestroy, AfterViewInit {
  @ViewChild('scrollContainer', { static: false }) scrollContainer!: ElementRef;

  private readonly router = inject(Router);
  private readonly materialTransferService = inject(MaterialTransferService);
  private readonly posProfileService = inject(PosProfileService);
  private readonly destroy$ = new Subject<void>();

  header = 'Material Transfer';
  private readonly PAGE_SIZE = 20;

  entries: WritableSignal<any[]> = signal([]);
  isLoading = signal(false);
  isLoadingMore = signal(false);
  allLoaded = signal(false);
  currentRange = signal({ start: 0, end: 0, total: 0 });
  searchQuery = signal('');

  // Computed for filtered entries (search)
  filteredEntries = computed(() => {
    const query = this.searchQuery().toLowerCase();
    const list = this.entries();
    if (!query) return list;
    return list.filter(item =>
      Object.values(item).some(val =>
        typeof val === 'string' && val.toLowerCase().includes(query)
      )
    );
  });

  constructor() {
    this.loadInitialEntries();
  }

  ngAfterViewInit() {
    this.setupScrollListener();
  }

  private setupScrollListener() {
    if (this.scrollContainer) {
      const element = this.scrollContainer.nativeElement;
      element.addEventListener('scroll', () => {
        this.handleScroll();
      });
    }
  }

  private handleScroll() {
    if (this.scrollContainer) {
      const element = this.scrollContainer.nativeElement;
      const { scrollTop, scrollHeight, clientHeight } = element;
      // Load more when user is near the bottom (within 100px)
      if (scrollHeight - scrollTop - clientHeight < 100 && !this.isLoadingMore() && !this.allLoaded()) {
        this.loadMoreEntries();
      }
    }
  }

  async loadInitialEntries() {
    this.isLoading.set(true);
    this.allLoaded.set(false);
    this.entries.set([]);
    this.currentRange.set({ start: 0, end: 0, total: 0 });
    this.searchQuery.set('');
    const profile = this.posProfileService.posProfile();
    if (!profile?.company) {
      this.isLoading.set(false);
      return;
    }
    try {
      const response = await lastValueFrom(this.materialTransferService.getMaterialTransfersByCompany(0, this.PAGE_SIZE));
      const data = response && response.data ? response.data : [];
      this.entries.set(data);
      this.currentRange.set({ start: 1, end: data.length, total: data.length });
      if (data.length < this.PAGE_SIZE) {
        this.allLoaded.set(true);
      }
    } catch (e) {
      this.entries.set([]);
      this.allLoaded.set(true);
    } finally {
      this.isLoading.set(false);
    }
  }

  async loadMoreEntries() {
    if (this.isLoadingMore() || this.allLoaded()) return;
    this.isLoadingMore.set(true);
    const currentCount = this.entries().length;
    const profile = this.posProfileService.posProfile();
    if (!profile?.company) {
      this.isLoadingMore.set(false);
      return;
    }
    try {
      const response = await lastValueFrom(this.materialTransferService.getMaterialTransfersByCompany(currentCount, this.PAGE_SIZE));
      const newData = response && response.data ? response.data : [];
      if (newData.length > 0) {
        const updated = [...this.entries(), ...newData];
        this.entries.set(updated);
        this.currentRange.set({ start: 1, end: updated.length, total: updated.length });
        if (newData.length < this.PAGE_SIZE) {
          this.allLoaded.set(true);
        }
      } else {
        this.allLoaded.set(true);
      }
    } catch (e) {
      this.allLoaded.set(true);
    } finally {
      this.isLoadingMore.set(false);
    }
  }

  onSearchUpdated(query: string) {
    this.searchQuery.set(query.trim());
  }

  navigateToDetails(name: string) {
    this.router.navigate([`/inventory/single-request-material-details/${name}`]);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
