import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

import { Subject, BehaviorSubject, debounceTime, distinctUntilChanged, map, switchMap, tap, finalize } from 'rxjs';
import { Item, MaterialItem, MaterialRequestType, InventoryItem } from '@models';

import { SelectOption, SharedModule } from '@elements/ui';
import { EditCartItemComponent } from '@features/invoices';
import { InventoryService, MaterialCartService, PosProfileService } from '@elevar-clients/api';

@Component({
  selector: 'lib-new-material-request',
  imports: [CommonModule, SharedModule, EditCartItemComponent],
  templateUrl: './new-material-request.component.html',
  styleUrl: './new-material-request.component.scss'
})
export class NewMaterialRequestComponent {
  private readonly router = inject(Router);
  private readonly _inventoryService = inject(InventoryService);
  private readonly _posService = inject(PosProfileService);

  readonly cart = inject(MaterialCartService);

  currentWarehouse = this._posService.posProfile()?.warehouse;

  private readonly searchSubject = new Subject<string>();
  private readonly searchResultsSubject = new BehaviorSubject<InventoryItem[]>([]);

  selectedItem: InventoryItem | null = null;
  isLoading = false;

  header = 'New Material Request';

  showEditModal = false;
  editingItem: any | null = null;

  items: MaterialItem[] = [];
  materialRequestTypeOptions: MaterialRequestType[] = [
    MaterialRequestType.PURCHASE,
    MaterialRequestType.MATERIAL_TRANSFER
  ];

  searchResults$ = this.searchResultsSubject.asObservable();

  formGroup = new FormGroup({
    search: new FormControl('', [Validators.required]),
    quantity: new FormControl('0', [Validators.required, Validators.min(1)]),
    materialRequestType: new FormControl(MaterialRequestType.PURCHASE, [Validators.required])
  });

  constructor() {
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      tap(() => {
        this.isLoading = true;
      }),
      switchMap(searchTerm => {
        return this._inventoryService.getWarehouseInventory(this.currentWarehouse as string).pipe(
          map(items => items.data.filter(item =>
            item.item_code.toLowerCase().includes(searchTerm.toLowerCase())
          )),
          finalize(() => this.isLoading = false)
        );
      })

    ).subscribe(results => {
      this.searchResultsSubject.next(results);
    });

    this.searchControl.valueChanges.subscribe(value => {
      if (value && !this.selectedItem) {
        this.searchSubject.next(value);
      } else if (value && this.selectedItem && value !== this.selectedItem.item_code) {
        // Clear selected item when user starts typing something different
        this.clearSelectedItem();
        this.searchSubject.next(value);
      } else if (!value) {
        this.clearSearchResults();
      }
    });

    this.materialRequestTypeControl.valueChanges.subscribe(value => {
      this.cart.updateMaterialRequestType(value);
    });
  }

  get searchControl(): FormControl {
    return this.formGroup.get('search') as FormControl;
  }

  get quantityControl(): FormControl {
    return this.formGroup.get('quantity') as FormControl;
  }

  get materialRequestTypeControl(): FormControl {
    return this.formGroup.get('materialRequestType') as FormControl;
  }

  navigatetoMaterialRequest() {
    this.router.navigate(['/inventory/request-material']);
  }

  get formatRequestTypeOptions(): SelectOption[] {
    return this.materialRequestTypeOptions.map(type => ({ value: type, label: type }));
  }

  startEditing(item: any) {
    if (item) {
      this.editingItem = item;
      this.showEditModal = true;
    }
  }

  clearSearchResults() {
    this.searchResultsSubject.next([]);
  }

  clearSelectedItem() {
    this.selectedItem = null;
    this.quantityControl.setValidators([
      Validators.required,
      Validators.min(1)
    ]);
    this.quantityControl.updateValueAndValidity();
  }

  private convertInventoryItemToItem(inventoryItem: InventoryItem): Item {
    return {
      item_code: inventoryItem.item_code,
      item_name: inventoryItem.item_code, // Using item_code as item_name since it's not available
      description: '', // Not available in InventoryItem
      item_group: '', // Not available in InventoryItem
      stock_uom: inventoryItem.stock_uom,
      disabled: 0, // Default value
      image: null, // Not available in InventoryItem
      price: undefined // Not available in InventoryItem
    };
  }

  selectItem(item: InventoryItem) {
    this.selectedItem = item;
    this.searchControl.setValue(item.item_code, { emitEvent: false });
    this.quantityControl.setValidators([
      Validators.required,
      Validators.min(1)
    ]);
    this.quantityControl.updateValueAndValidity();
    this.clearSearchResults();
  }

  handleEditSave($event: any) {
    if (this.formGroup.valid && this.selectedItem) {
      if (this.cart.hasItem(this.selectedItem.item_code)) {
        // Update existing item
        const existingItem = this.cart.getItem(this.selectedItem.item_code);
        if (existingItem) {
          this.cart.updateItem({
            ...existingItem,
            quantity: Number(this.quantityControl.value)
          });
          this.resetForm();
          return;
        }
      }

      // Add new item to cart
      this.cart.addItem(
        this.convertInventoryItemToItem(this.selectedItem),
        Number(this.quantityControl.value)
      );
      this.resetForm();
    }
  }

  addItem() {
    const item = {
      item_code: this.formGroup.value.search as string,
      qty: this.formGroup.value.quantity ?? 0
    }
    this.items.push(item)
  }

  resetForm() {
    this.formGroup.reset({
      search: '',
      quantity: '0',
      materialRequestType: MaterialRequestType.PURCHASE
    });
    this.selectedItem = null;
    this.clearSearchResults();
  }

  get isAddItemDisabled(): boolean {
    return !this.selectedItem || !this.formGroup.valid;
  }

  get addItemButtonType(): 'loading' | 'disabled' | 'default' {
    if (this.isLoading) return 'loading';
    if (this.isAddItemDisabled) return 'disabled';
    return 'default';
  }

  handleAddItem() {
    if (this.formGroup.valid && this.selectedItem) {
      if (this.cart.hasItem(this.selectedItem.item_code)) {
        const existingItem = this.cart.getItem(this.selectedItem.item_code);
        if (existingItem) {
          this.startEditing(this.selectedItem.item_code);
          this.resetForm();
          return;
        }
      }

      this.cart.addItem(
        this.convertInventoryItemToItem(this.selectedItem),
        Number(this.quantityControl.value)
      );
      this.resetForm();
    }

  }

}
