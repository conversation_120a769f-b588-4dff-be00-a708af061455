import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';

import { Router } from '@angular/router';

import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';

import { Subject, BehaviorSubject, finalize, takeUntil, take } from 'rxjs';

import { InventoryItem, Item, StockReconciliationItem } from '@models';
import { ButtonComponent, ElevarInputsComponent, PageFooterComponent, PageHeaderComponent } from '@elements/ui';

import { NotificationService } from '@core';
import { EditRecordComponent } from '../edit-record/edit-record.component';
import { InventoryService, PosProfileService } from '@elevar-clients/api';

@Component({
  selector: 'lib-new-stock-reconciliation',
  imports: [CommonModule, ButtonComponent, PageHeaderComponent, PageFooterComponent, EditRecordComponent, FormsModule, ReactiveFormsModule, ButtonComponent, ElevarInputsComponent],
  templateUrl: './new-stock-reconciliation.component.html',
  styleUrl: './new-stock-reconciliation.component.scss'
})
export class NewStockReconciliationComponent {
  header = 'New Stock Reconciliation';
  private readonly destroy$ = new Subject<void>();
  private router = inject(Router);
  private readonly _posService = inject(PosProfileService);
  private readonly _notificationService = inject(NotificationService);
  private readonly _inventoryService = inject(InventoryService);

  private readonly searchResultsSubject = new BehaviorSubject<InventoryItem[]>([]);
  allItems: InventoryItem[] = [];

  showEditModal = false;
  editingItem: any | null = null;


  selectedItem: Item | null = null;
  availableStock = 0;

  isLoading = false;
  searchResults$ = this.searchResultsSubject.asObservable();

  currentBranch = this._posService.posProfile()?.branch;
  currentWarehouse = this._posService.posProfile()?.warehouse;

  allItems$ = this._inventoryService.getWarehouseInventory(this.currentWarehouse as string);
  items: StockReconciliationItem[] = [];

  formGroup = new FormGroup({
    search: new FormControl(''),
    storeName: new FormControl({ value: this.currentBranch, disabled: true }) ,
    quantity: new FormControl(0, [Validators.required, Validators.min(1)])
  });

  constructor() {
    this.isLoading = true;
    this._inventoryService.getWarehouseInventory(this.currentWarehouse as string).pipe(
      finalize(() => this.isLoading = false)
    ).subscribe(res => {
      this.allItems = res.data;
      this.searchResultsSubject.next(this.allItems);
    });

    this.searchControl.valueChanges.subscribe(value => {
      if (value && !this.selectedItem) {
        const filtered = this.allItems.filter(item =>
          item.item_code.toLowerCase().includes(value.toLowerCase())
        );
        this.searchResultsSubject.next(filtered);
      } else if (!value) {
        this.searchResultsSubject.next(this.allItems);
      }
    });
  }

  get storeControl(): FormControl {
    return this.formGroup.get('storeName') as FormControl;
  }

  get searchControl(): FormControl {
    return this.formGroup.get('search') as FormControl;
  }

  get quantityControl(): FormControl {
    return this.formGroup.get('quantity') as FormControl;
  }

  handleEditSave(updatedItem: any) {
    const index = this.items.findIndex(item => item.item_code === updatedItem.item_code);
    if (index !== -1) {
      this.items[index] = updatedItem;
    } else {
      console.warn('Item not found for update:', updatedItem.item_code);
    }
  }

  handleAddItem() {
    if (this.selectedItem && !this.items.some(item => item.item_code === this.selectedItem?.item_code)) {
      this.items.push({
        ...this.selectedItem,
        image: null,
        quantity: this.formGroup.value.quantity ?? 0
      } as StockReconciliationItem);

      this.resetForm();
    } else {
      this._notificationService.setWarning('Duplicate Item', 'This item already exists in the list. Please check your entry.');
    }
  }

  addAllItems() {
    this.allItems$.pipe(take(1)).subscribe((a) => {
      if (a?.data?.length) {
        a.data.forEach(item => {
          if (!this.items.some(existing => existing.item_code === item.item_code)) {
            this.items.push({
              ...item,
              image: null,
              quantity: item.actual_qty
            } as unknown as StockReconciliationItem);
          } else {
            this._notificationService.setWarning('Existing', `This item is already in the list ${item.item_code as string}`);
          }
        });
      }
    });
  }

  validateQuantity() {
    if (this.quantityControl.value > this.availableStock) {
      this.quantityControl.setErrors({ stockExceeded: true });
    } else {
      this.quantityControl.setErrors(null);
    }
  }

  handleEditDelete({ item_code }: any) {
    if(item_code){
      this.items = this.items.filter(item => item.item_code !== item_code);
    }
  }

  resetForm() {
    this.formGroup.reset({
      search: '',
      storeName: this.currentBranch
    });
    this.selectedItem = null;
    this.clearSearchResults();
  }

  selectItem(item: Item) {
    this.selectedItem = item;
    this.searchControl.setValue(item.item_code, { emitEvent: false });
    this.quantityControl.setValidators([
      Validators.required,
      Validators.min(1),
      Validators.max((item as any).actual_qty)
    ])
    this.availableStock = (item as any).actual_qty;
    this.clearSearchResults();
  }

  clearSearchResults() {
    this.searchResultsSubject.next([]);
  }

  submitStockReconciliation() {
    const company = this._posService.posProfile()?.company;
    const selectedItems = this.items;

    if(!company) {
      return;
    }

    this.isLoading = true;

    this._inventoryService.saveStockReconciliations(selectedItems)
    .pipe(
      takeUntil(this.destroy$),
      finalize(() => {
        this.isLoading = false;
      })
    )
    .subscribe({
      next: (response) => {
        this._notificationService.setSuccess('Reconciliation submitted successfully', 'A new reconciliation has been submitted');
        this.navigateToInvoicesList();
      },
      error: (error) => {
        this.isLoading = false;
        this._notificationService.setError('Stock Reconciliation', `Creation of Stock Reconciliation entry failed because of ${JSON.stringify(error)}`);
      }
    });
  }

  startEditing(item: any) {
    if (item) {
      this.editingItem = item;
      this.showEditModal = true;
    }
  }

  navigateToInvoicesList() {
    this.router.navigate(['/inventory/stock-reconciliation']);
  }

  get isAddItemDisabled(): boolean {
    return !this.selectedItem || !this.formGroup.valid;
  }

  get isNextButtonDisabled(): boolean {
    return !this.items.length ;
  }

  get addItemButtonType(): 'loading' | 'disabled' | 'default' {
    if (this.isLoading) return 'loading';
    if (this.isAddItemDisabled) return 'disabled';
    return 'default';
  }

  onSearchFocus() {
    this.searchResultsSubject.next(this.allItems);
  }
}
