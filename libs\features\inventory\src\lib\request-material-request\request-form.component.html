<lib-page-header [header]="header" [showArrowIcon]="true" />

<main class="flex-grow p-4 sm:p-6 lg:p-8 bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
  <div class="max-w-2xl mx-auto">
    <!-- Status Card -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6 mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-lg sm:text-xl font-semibold text-gray-900 mb-1">Material Request Summary</h1>
          <p class="text-sm text-gray-600">Review your request before submitting</p>
        </div>
        <div class="flex items-center">
          <span [ngClass]="statusClass"
                class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-blue-100 text-blue-700 border border-blue-200">
            <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
            {{ statusText }}
          </span>
        </div>
      </div>
    </div>

    <!-- Items List -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div class="px-4 sm:px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h2 class="text-base font-semibold text-gray-900">Requested Items</h2>
        <p class="text-sm text-gray-600 mt-1">{{ cart.items().length }} item{{ cart.items().length !== 1 ? 's' : '' }} in request</p>
      </div>

      <div class="divide-y divide-gray-200">
        @if (cart.items().length === 0) {
          <div class="p-8 text-center">
            <div class="mx-auto w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
              </svg>
            </div>
            <h3 class="text-sm font-medium text-gray-900 mb-1">No items in request</h3>
            <p class="text-sm text-gray-500">Add items to your material request to continue</p>
          </div>
        }

        @for (item of cart.items(); track item.item_code; let isLast = $last) {
          <div class="p-4 sm:p-6 hover:bg-gray-50 transition-colors duration-200">
            <div class="flex items-start justify-between">
              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                      </svg>
                    </div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <h3 class="text-sm font-semibold text-gray-900 truncate">{{ item.item_code | titlecase }}</h3>
                    <p class="text-xs text-gray-500 mt-0.5">Item Code</p>
                  </div>
                </div>
              </div>

              <div class="flex items-center space-x-4">
                <div class="text-right">
                  <div class="flex items-center space-x-2">
                    <span class="text-lg font-semibold text-gray-900">{{ item.quantity }}</span>
                    <span class="text-xs font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded-full">KG</span>
                  </div>
                  <p class="text-xs text-gray-500 mt-0.5">Quantity</p>
                </div>

                @if (!isLast) {
                  <div class="hidden sm:block w-px h-8 bg-gray-200"></div>
                }
              </div>
            </div>
          </div>
        }
      </div>
    </div>

    <!-- Action Buttons -->
    @if (!isTransfer) {
      <div class="mt-6 sm:mt-8">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6">
          <div class="flex flex-col sm:flex-row gap-3">
            <button type="button"
                    class="flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors duration-200 text-sm sm:text-base">
              <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              Back to Edit
            </button>
            <lib-button type="submit"
                       class="flex-1 px-4 py-3 text-sm sm:text-base font-medium"
                       [buttonType]="loading ? 'loading' : 'default'"
                       [disabled]="loading"
                       (click)="navigatetoMaterialRequest()">
              @if (loading) {
                <svg class="w-4 h-4 mr-2 inline animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                {{ 'Submitting...' }}
              } @else {
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                </svg>
                {{ 'Submit Request' }}
              }
            </lib-button>
          </div>
        </div>
      </div>
    }

    @if (isTransfer) {
      <div class="mt-6 sm:mt-8">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6">
          <h3 class="text-sm font-semibold text-gray-900 mb-4">Transfer Actions</h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <button class="flex items-center justify-center px-4 py-3 bg-indigo-100 text-indigo-700 rounded-lg font-medium hover:bg-indigo-200 transition-colors duration-200 text-sm sm:text-base">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
              </svg>
              Action 1
            </button>
            <button class="flex items-center justify-center px-4 py-3 bg-green-100 text-green-700 rounded-lg font-medium hover:bg-green-200 transition-colors duration-200 text-sm sm:text-base">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Action 2
            </button>
          </div>
        </div>
      </div>
    }
  </div>
</main>

<lib-page-footer />
