import { Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@elements/ui';
import { NotificationService } from '@core';
import { MaterialCartService, MaterialRequestService } from '@elevar-clients/api';

@Component({
  selector: 'lib-request-form',
  standalone: true,
  imports: [CommonModule, SharedModule],
  templateUrl: './request-form.component.html',
  styleUrl: './request-form.component.scss'
})
export class RequestFormComponent implements OnInit {

  private readonly router = inject(Router);
  private readonly route =  inject(ActivatedRoute);
  private readonly _notificationService = inject(NotificationService);
  private readonly _materialService = inject(MaterialRequestService);

  readonly cart = inject(MaterialCartService);

  clue!: string;
  isTransfer = false;
  header = 'Request Form';
  loading = false;

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.clue = params['clue'];
      if(this.clue === 'transfer') {
        this.isTransfer = true;
      }
    });

    // Check if there are no cart items on init
    if (this.cart.isEmpty()) {
      this._notificationService.setError('No items in cart', 'Please add items to your cart before making a request.');
      this.router.navigate(['/inventory/material-request']);
    }
  }

  navigatetoMaterialRequest() {
    if (!this.isTransfer) {
      this.loading = true;
      this._materialService.createMaterialRequest().subscribe({
        next: (response) => {
          const materialRequestId = response.data['name'];
          if (!materialRequestId) {
            this._notificationService.setError(
              'Failed to create material request',
              'No material request ID returned from the server'
            );
            this.loading = false;
            return;
          }

          this._materialService.submitMaterialRequestAsPending(materialRequestId).subscribe({
            next: () => {
              this.cart.clearCart();
              this._notificationService.setSuccess(
                'Request submitted as pending',
                'The material request has been submitted for approval'
              );
              this.router.navigate(['/inventory/material-request']);
              this.loading = false;
            },
            error: () => {
              this._notificationService.setError(
                'Failed to submit request as pending',
                'The draft was created, but an error occurred while submitting for approval'
              );
              this.cart.clearCart();
              this.router.navigate(['/inventory/material-request']);
              this.loading = false;
            }
          });
        },
        error: () => {
          this._notificationService.setError(
            'Failed to create material request',
            'An error occurred while creating the material request'
          );
          this.loading = false;
        }
      });
    } else {
      this.loading = true;
      // For transfer requests, also clear the cart after successful transfer
      this.cart.clearCart();
      this._notificationService.setSuccess('Transfer completed', 'Material transfer has been completed successfully');
      this.router.navigate(['/inventory/material-request']);
      this.loading = false;
    }
  }

  get statusText(): string {
    return this.isTransfer ? 'TRANSFERRED' : 'DRAFT';
  }

  get statusClass(): string {
    return this.isTransfer ? 'text-sm text-blue-500 bg-blue-100 p-2 rounded-md' : 'text-sm text-gray-500';
  }
}
