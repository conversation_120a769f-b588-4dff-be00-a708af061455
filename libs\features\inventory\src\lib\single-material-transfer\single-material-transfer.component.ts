import { Component, inject, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { SharedModule } from '@elements/ui';

import { MaterialTransferStore } from '@state';
import { NotificationService } from '@core';
import { MaterialTransferService } from '@elevar-clients/api';


@Component({
  selector: 'lib-single-material-transfer',
  imports: [CommonModule, SharedModule],
  templateUrl: './single-material-transfer.component.html',
  styleUrl: './single-material-transfer.component.scss'
})
export class SingleMaterialTransferComponent implements OnDestroy {
  private readonly _route = inject(ActivatedRoute);
  requestMaterialStore = inject(MaterialTransferStore);
  private readonly _materialTransferService = inject(MaterialTransferService);
  private readonly _notificationService = inject(NotificationService);

  id = this._route.snapshot.params['id'];

  select({ id }: any) {
    this.requestMaterialStore.selectEntity({ id });
    this.requestMaterialStore.loadMaterialTransfer({ id });
  }

  accordionState = {
    header: true,
    items: true,
    payments: true,
    summary: true
  };

  toggleAccordion(section: 'header' | 'items' | 'payments' | 'summary'): void {
    this.accordionState[section] = !this.accordionState[section];
  }

  constructor() { this.select({ id: this.id }) }

  selectedItem: any = null;

  openItemModal(item: any) {
    this.selectedItem = item;
  }

  closeItemModal() {
    this.selectedItem = null;
  }

  isLoading = false;

  get productDetail() {
    return this.requestMaterialStore.loadMaterialTransferResult();
  }

  private readonly destroy$ = new Subject<void>();

  handleWorkflowAction(action: 'Received' | 'Rejected') {
    if (!this.id) return;
    const confirmMsg = action === 'Received'
      ? 'Are you sure you want to mark this material transfer as Received?'
      : 'Are you sure you want to Reject this material transfer?';
    if (!window.confirm(confirmMsg)) return;
    this.isLoading = true;
    this._materialTransferService.updateMaterialTransfer(this.id, action)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this._notificationService.setSuccess('Material Transfer Updated', `Material transfer marked as ${action}.`);
          this.requestMaterialStore.loadMaterialTransfer({ id: this.id });
          this.isLoading = false;
        },
        error: (err) => {
          this._notificationService.setError('Update Failed', 'Could not update material transfer.');
          this.isLoading = false;
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
