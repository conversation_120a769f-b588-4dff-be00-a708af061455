@if(stock$ | async; as stock ) {
<div class="flex flex-col h-[calc(100vh-80px)] bg-gray-50">
    <lib-page-header [header]="stock.name" [showArrowIcon]="true" [isSearchable]="false" />

    <main class="flex-grow p-4 overflow-y-auto space-y-4">
        <div class="rounded-md overflow-hidden border border-gray-200 shadow-sm">
            <button (click)="toggleAccordion('header')" class="flex justify-between items-center w-full p-4"
                [ngClass]="accordionState.header ? 'bg-primary-hoverGreen text-white' : 'bg-primary-greenLight text-white'"
                type="button">
                <h2 class="text-base font-medium">Stock Details</h2>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transition-transform"
                    [ngClass]="accordionState.header ? 'rotate-180' : ''" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
            </button>
        </div>
        @if(accordionState.header) {
            <div class="bg-white p-4">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-light text-gray-700 mb-1">Company</label>
                  <p class="text-sm font-medium text-gray-900">{{ stock.company }}</p>
                </div>
                <div>
                  <label class="block text-sm font-light text-gray-700 mb-1">Branch</label>
                  <p class="text-sm font-medium text-gray-900">{{ stock.branch  }}</p>
                </div>
              </div>
            </div>
            }
            <div class="rounded-md overflow-hidden border border-gray-200 shadow-sm">
                <button (click)="toggleAccordion('items')" class="flex justify-between items-center w-full p-4"
                  [ngClass]="accordionState.items ? 'bg-primary-hoverGreen text-white' : 'bg-primary-greenLight text-white'"
                  type="button">
                  <div class="flex items-center">
                    <h2 class="text-base font-medium">Items</h2>
                    @if(stock.items && stock.items.length > 0) {
                    <span class="ml-2 text-xs bg-white text-primary-hoverGreen rounded-full px-2 py-0.5">
                      {{ stock.items.length }}
                    </span>
                    }
                  </div>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transition-transform"
                    [ngClass]="accordionState.items ? 'rotate-180' : ''" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                @if(accordionState.items) {
                <div class="overflow-x-auto">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr class="bg-primary-greenLight">
                        <th class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-medium text-white w-1/4">
                          Item Name
                        </th>
                        <th class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-medium text-white w-1/4">
                          Qty
                        </th>
                        <th class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-medium text-white w-1/4">
                          Price
                        </th>
                        <th class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-medium text-white w-1/4">
                          Amount
                        </th>
                      </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 bg-white">
                      @if (!stock.items || stock.items.length === 0) {
                      <tr>
                        <td colspan="4" class="px-4 py-8 text-center text-sm text-gray-500">
                          <div class="flex flex-col items-center justify-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24"
                              stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <span>No items in invoice</span>
                          </div>
                        </td>
                      </tr>
                      }
                      @for(item of stock.items; track item.item_code) {
                      <tr class="hover:bg-gray-50 transition-colors">
                        <td class="whitespace-nowrap px-2 py-4 text-sm">
                          <div class="flex flex-col">
                            <span class="font-medium text-gray-900">{{ item.item_code | titlecase }}</span>
                          </div>
                        </td>
                        <td class="whitespace-nowrap px-2 py-4 text-sm text-center">
                          <div class="flex items-center justify-center">
                            <span
                              class="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700">
                              {{ item.qty }}
                            </span>
                          </div>
                        </td>
                        <td class="whitespace-nowrap px-2 py-4 text-sm text-gray-600 text-center">
                          <span
                            class="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
                            {{ item.current_amount }}
                          </span>
                        </td>
                        <td class="whitespace-nowrap px-2 py-4 text-sm text-gray-600 text-center">
                          {{ item.amount}}
                        </td>
                      </tr>
                      }
                    </tbody>
                  </table>
                </div>
                }
              </div>
    </main>
</div>
}
