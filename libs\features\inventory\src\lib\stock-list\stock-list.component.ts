import { Component, inject, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { LoaderComponent, SelectOption, SharedModule, TemplateDrivenInputComponent } from '@elements/ui';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { catchError, finalize, takeUntil, tap } from 'rxjs/operators';
import { of, Subject } from 'rxjs';
import { InventoryService, PosProfileService } from '@elevar-clients/api';

@Component({
  selector: 'lib-stock-list',
  imports: [CommonModule, SharedModule, LoaderComponent, TemplateDrivenInputComponent, FormsModule],
  templateUrl: './stock-list.component.html',
  styleUrl: './stock-list.component.scss'
})
export class StockListComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  private readonly _inventoryService = inject(InventoryService);
  readonly _posService = inject(PosProfileService);

  currentWarehouse = this._posService.posProfile()?.warehouse;
  allWarehouses$ = this._posService.getWarehousesForCompany().pipe(
    tap(warehouses => {
      this.allWareHousSelectOptions = warehouses.map(warehouse => ({
        label: warehouse.warehouse_name,
        value: warehouse.warehouse_name
      }));

    }),
    catchError(error => {
      console.error('Error loading warehouses:', error);
      this.error = error;
      return of([]);
    })
  );

  error: any;
  allWareHousSelectOptions: SelectOption[] = [];
  defaultSelectedOption!: SelectOption
  header = 'Stock List';
  isLoading = false;

  // Original inventory from API
  inventory: { item: string, quantity: number, uom: string }[] = [];
  // Filtered inventory for display
  filteredInventory: { item: string, quantity: number, uom: string }[] = [];

  searchTerm = '';
  sortField: 'item' | 'quantity' | null = null;
  sortDirection: 'asc' | 'desc' = 'asc';

  formGroup = new FormGroup({
    storeName: new FormControl(this.currentWarehouse, [Validators.required]),
  });

  ngOnInit() {
    this.loadInventory();

    this.formGroup.controls.storeName.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => {
        this.currentWarehouse = value ?? this._posService.posProfile()?.warehouse;
        if (value && this.formGroup.valid) {
          this.loadInventory();
        }
      });

    this.onWarehouseChange(this.currentWarehouse);
  }

  onWarehouseChange(event: any) {
    this.formGroup.controls.storeName.patchValue(event);
    this.loadInventory();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadInventory() {
    const warehouse = this.formGroup.controls.storeName.value;
    if (!warehouse) return;

    this.isLoading = true;
    //TEMPORARY FIX We use suffix 'ELGR' for all warehouses, we should fetch the company abbreviation in warehouse. backend does not return it as at 22/3/2025
    const request = warehouse.includes('ELGR') ? warehouse : `${warehouse} - ELGR`;
    this._inventoryService.getWarehouseInventory(request)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => this.isLoading = false)
      )
      .subscribe({
        next: (response) => {
          this.inventory = response.data.map(item => ({
            item: item.item_code,
            quantity: item.actual_qty,
            uom: item.stock_uom
          }));
          this.filterInventory();
        },
        error: (error) => {
          console.error('Error loading inventory:', error);
          this.inventory = [];
          this.filteredInventory = [];
        }
      });
  }

  filterInventory() {
    let tempInventory = [...this.inventory];

    // Apply search filter
    if (this.searchTerm) {
      tempInventory = tempInventory.filter(item =>
        item.item.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }

    // Apply sorting
    if (this.sortField) {
      tempInventory.sort((a, b) => {
        const valueA = a[this.sortField!];
        const valueB = b[this.sortField!];

        if (typeof valueA === 'number' && typeof valueB === 'number') {
          return this.sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
        }
        return this.sortDirection === 'asc'
          ? String(valueA).localeCompare(String(valueB))
          : String(valueB).localeCompare(String(valueA));
      });
    }

    this.filteredInventory = tempInventory;
  }

  sortInventory(field: 'item' | 'quantity') {
    if (this.sortField === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = field;
      this.sortDirection = 'asc';
    }
    this.filterInventory();
  }
}