<lib-page-header [header]="header" [showArrowIcon]="true" [isSearchable]="true" (search)="onSearchUpdated($event)"/>
<div class="flex flex-col h-[calc(100vh-80px)] bg-gray-50">
  <!-- Range Display -->
  @if (filteredReconciliations().length > 0) {
    <div class="px-4 py-2 bg-gray-100 border-b border-gray-200">
      <p class="text-sm text-gray-600">
        Showing {{ currentRange().start }} - {{ currentRange().end }} of reconciliations
      </p>
    </div>
  }
  <main #scrollContainer class="flex-1 overflow-auto p-4 space-y-4 max-h-[calc(100vh-255px)]">
    <lib-loader *ngIf="isLoading()"></lib-loader>
    @for(item of filteredReconciliations(); track item.name) {
      <lib-product-card [document]="item" [type]="'stock-reconc'" (click)="navigateToStockDetail(item.name)"></lib-product-card>
    } @empty {
      <div class="text-center text-gray-500 p-4">
        No stock reconciliation records found
      </div>
    }
    <lib-loader *ngIf="isLoadingMore() && !allLoaded()"></lib-loader>
    <!-- End of List Message -->
    @if (allLoaded() && filteredReconciliations().length > 0) {
      <div class="flex justify-center py-4">
        <p class="text-sm text-gray-500">You've reached the end of the list. ✨</p>
      </div>
    }
  </main>
  <lib-button type="submit" class="fixed right-4 bottom-20 rounded-full" (click)="navigateToNewStock()">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2">
      <line x1="12" y1="5" x2="12" y2="19"></line>
      <line x1="5" y1="12" x2="19" y2="12"></line>
    </svg>
    <span>New Reconciliation</span>
  </lib-button>

  <lib-page-footer />
</div>
