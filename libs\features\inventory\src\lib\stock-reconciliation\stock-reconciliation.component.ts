import { Component, inject, On<PERSON><PERSON>roy, ViewChild, ElementRef, AfterViewInit, Signal, WritableSignal, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { LoaderComponent, SharedModule } from '@elements/ui';
import { InventoryService, PosProfileService } from '@elevar-clients/api';
import { StockReconciliation } from '@models';

@Component({
  selector: 'lib-stock-reconciliation',
  imports: [CommonModule, SharedModule, LoaderComponent],
  templateUrl: './stock-reconciliation.component.html',
  styleUrl: './stock-reconciliation.component.scss'
})
export class StockReconciliationComponent implements OnDestroy, AfterViewInit {
  @ViewChild('scrollContainer', { static: false }) scrollContainer!: ElementRef;

  private readonly router = inject(Router);
  private readonly inventoryService = inject(InventoryService);
  private readonly posProfileService = inject(PosProfileService);
  private readonly destroy$ = new Subject<void>();

  header = 'Stock Reconciliation';
  private readonly PAGE_SIZE = 20;

  reconciliations: WritableSignal<StockReconciliation[]> = signal([]);
  isLoading = signal(false);
  isLoadingMore = signal(false);
  allLoaded = signal(false);
  currentRange = signal({ start: 0, end: 0, total: 0 });
  searchQuery = signal('');

  // Computed for filtered reconciliations (search)
  filteredReconciliations = computed(() => {
    const query = this.searchQuery().toLowerCase();
    const list = this.reconciliations();
    if (!query) return list;
    return list.filter(item =>
      Object.values(item).some(val =>
        typeof val === 'string' && val.toLowerCase().includes(query)
      )
    );
  });

  constructor() {
    this.loadInitialReconciliations();
  }

  ngAfterViewInit() {
    this.setupScrollListener();
  }

  private setupScrollListener() {
    if (this.scrollContainer) {
      const element = this.scrollContainer.nativeElement;
      element.addEventListener('scroll', () => {
        this.handleScroll();
      });
    }
  }

  private handleScroll() {
    if (this.scrollContainer) {
      const element = this.scrollContainer.nativeElement;
      const { scrollTop, scrollHeight, clientHeight } = element;
      // Load more when user is near the bottom (within 100px)
      if (scrollHeight - scrollTop - clientHeight < 100 && !this.isLoadingMore() && !this.allLoaded()) {
        this.loadMoreReconciliations();
      }
    }
  }

  async loadInitialReconciliations() {
    this.isLoading.set(true);
    this.allLoaded.set(false);
    this.reconciliations.set([]);
    this.currentRange.set({ start: 0, end: 0, total: 0 });
    this.searchQuery.set('');
    const profile = this.posProfileService.posProfile();
    if (!profile?.company) {
      this.isLoading.set(false);
      return;
    }
    try {
      const response = await this.inventoryService.getStockReconciliations(profile.company, 0, this.PAGE_SIZE).toPromise();
      const data = response && response.data ? response.data : [];
      this.reconciliations.set(data);
      this.currentRange.set({ start: 1, end: data.length, total: data.length });
      if (data.length < this.PAGE_SIZE) {
        this.allLoaded.set(true);
      }
    } catch (e) {
      this.reconciliations.set([]);
      this.allLoaded.set(true);
    } finally {
      this.isLoading.set(false);
    }
  }

  async loadMoreReconciliations() {
    if (this.isLoadingMore() || this.allLoaded()) return;
    this.isLoadingMore.set(true);
    const currentCount = this.reconciliations().length;
    const profile = this.posProfileService.posProfile();
    if (!profile?.company) {
      this.isLoadingMore.set(false);
      return;
    }
    try {
      const response = await this.inventoryService.getStockReconciliations(profile.company, currentCount, this.PAGE_SIZE).toPromise();
      const newData = response && response.data ? response.data : [];
      if (newData.length > 0) {
        const updated = [...this.reconciliations(), ...newData];
        this.reconciliations.set(updated);
        this.currentRange.set({ start: 1, end: updated.length, total: updated.length });
        if (newData.length < this.PAGE_SIZE) {
          this.allLoaded.set(true);
        }
      } else {
        this.allLoaded.set(true);
      }
    } catch (e) {
      this.allLoaded.set(true);
    } finally {
      this.isLoadingMore.set(false);
    }
  }

  onSearchUpdated(query: string) {
    this.searchQuery.set(query.trim());
  }

  navigateToNewStock() {
    this.router.navigate(['/inventory/new-stock-reconciliation']);
  }

  navigateToStockDetail(name: string) {
    this.router.navigateByUrl(`/inventory/details/${name}`);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
