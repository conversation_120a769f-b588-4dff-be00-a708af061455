export function normalizeTime(time: string): string {
    const [hour, minute, rest] = time.split(':');
    const paddedHour = hour.padStart(2, '0');
    const paddedMinute = minute.padStart(2, '0');
    
    const [second, micro] = rest.split('.');
    const paddedSecond = second.padStart(2, '0');
    const milli = micro?.slice(0, 3) || '000'; 
  
    return `${paddedHour}:${paddedMinute}:${paddedSecond}.${milli}`;
  }