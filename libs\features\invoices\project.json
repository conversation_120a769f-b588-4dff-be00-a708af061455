{"name": "invoices", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/features/invoices/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/features/invoices/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/features/invoices/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/features/invoices/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/features/invoices/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}