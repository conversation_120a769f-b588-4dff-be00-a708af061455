import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';

import { ButtonComponent, ElevarInputsComponent } from "@elements/ui";
import { CartItem } from '@models';
import { CartService, InventoryService, PosProfileService } from '@elevar-clients/api';

@Component({
  selector: 'lib-edit-cart-item',
  imports: [CommonModule, ReactiveFormsModule, ElevarInputsComponent, ButtonComponent],
  templateUrl: './edit-cart-item.component.html',
  styleUrl: './edit-cart-item.component.scss'
})
export class EditCartItemComponent implements OnInit {
  private readonly cartService = inject(CartService);
  private readonly _posService = inject(PosProfileService);
  private readonly _inventoryService = inject(InventoryService);

  @Input() item!: CartItem;
  @Input() type!: string;

  @Output() save = new EventEmitter<CartItem>();
  @Output() close = new EventEmitter<void>();
  currentWarehouse = this._posService.posProfile()?.warehouse;


  formGroup = new FormGroup<any>({
    item: new FormControl('', [Validators.required]),
    quantity: new FormControl(0, [Validators.required, Validators.min(0.001)]),
    uom: new FormControl('')
  });

  ngOnInit() {
    if (this.item) {
      // Ensure quantity is at least 0.001
      const safeQuantity = Math.max(this.item.quantity, 0.001);

      this.formGroup.patchValue({
        item: this.item.item_code,
        quantity: safeQuantity,
        uom: this.item.stock_uom
      });

      this.fetchInventoryAndSetValidation();
    }
  }

  private fetchInventoryAndSetValidation() {
    if (!this.currentWarehouse) return;

    this._inventoryService.getWarehouseInventory(this.currentWarehouse)
      .subscribe(inventory => {
        const maxQuantity = this.getMaxQuantityForItem(this.item.item_code, inventory.data);
        this.quantityControl.setValidators([
          Validators.required,
          Validators.min(0.001),
          Validators.max(maxQuantity)
        ]);
        this.quantityControl.updateValueAndValidity();
      });
  }


  private getMaxQuantityForItem(itemCode: string, inventory: any[]): number {
    const itemData = inventory.find(i => i.item_code == itemCode);
    return itemData ? itemData.actual_qty || 1 : 1;
  }

  handleSave() {
    if (this.formGroup.valid && !this.isSaveDisabled) {
      const quantity = Number(this.quantityControl.value);
      const formattedQuantity = Math.round(quantity * 1000) / 1000; // Ensure 3 decimal places

      const updatedItem: CartItem = {
        ...this.item,
        quantity: formattedQuantity
      };
      this.save.emit(updatedItem);
      this.close.emit();
    }
  }

  onRemove() {
    this.cartService.removeItem(this.item.item_code);
    this.close.emit();
  }

  get itemControl(): FormControl {
    return this.formGroup.get('item') as FormControl;
  }

  get quantityControl(): FormControl {
    return this.formGroup.get('quantity') as FormControl;
  }

  get uomControl(): FormControl {
    return this.formGroup.get('uom') as FormControl;
  }

  get isSaveDisabled(): boolean {
    const quantity = Number(this.quantityControl.value);
    return !this.formGroup.valid || quantity < 0.001;
  }
}
