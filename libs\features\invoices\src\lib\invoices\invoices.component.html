@if (trackOpeningEntry$ | async) {}

@if (!isLoading() || invoices().length > 0) {
<div class="flex flex-col h-[calc(100vh-80px)] bg-gray-50">
  <lib-page-header [header]="header" [showArrowIcon]="false" [isSearchable]="true" [placeholder]="'Search Invoice'"
    (search)="onSearchUpdated($event)" />

  @if(hasOpeningEntryToday()){
  <div class="flex items-center justify-between p-4 border-b border-gray-200">
    <div class="flex items-center space-x-2">
      <div class="h-2 w-2 bg-green-500 rounded-full animate-blink"></div>
      <span class="text-sm font-medium text-gray-700">POS is open</span>
    </div>
    <button (click)="navigateToCloseEntries()"
      class="text-blue-600 hover:text-blue-800 text-sm font-medium focus:outline-none">
      Close
    </button>
  </div>
  }

  <!-- Range Display -->
  @if (invoices().length > 0) {
  <div class="px-4 py-2 bg-gray-100 border-b border-gray-200">
    <p class="text-sm text-gray-600">
      Showing {{ currentRange().start }} - {{ currentRange().end }} of invoices
    </p>
  </div>
  }

  <main class="flex-1 overflow-y-auto p-4 space-y-4 max-h-[calc(100vh-80px)]" #scrollContainer>
    @for (invoice of invoices(); track invoice.name) {
    <lib-product-card [document]="invoice" [type]="'invoice'"
      (click)="navigateToInvoiceDetail(invoice.name)"></lib-product-card>
    }

    <!-- Animated Loading Icon -->
    @if (isLoadingMore()) {
    <div class="flex justify-center py-4">
      <div class="relative">
        <div class="w-8 h-8 border-4 border-primary-lightGreen border-t-primary-lightGreen rounded-full animate-spin"></div>
        <div class="absolute inset-0 flex items-center justify-center">
          <div class="w-4 h-4 bg-primary-lightGreen rounded-full animate-pulse"></div>
        </div>
      </div>
    </div>
    }

    <!-- Load More Trigger -->
    @if (!allInvoicesLoaded() && !isLoadingMore()) {
    <div class="flex justify-center py-4">
      <button (click)="loadMore()"
        class="flex items-center justify-center px-4 py-2 text-sm text-primary-lightGreen hover:text-primary-lightGreen focus:outline-none">
        <svg class="w-4 h-4 mr-1 animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
        Load more
      </button>
    </div>
    }

    <!-- End of List Message -->
    @if (allInvoicesLoaded() && invoices().length > 0) {
    <div class="flex justify-center py-4">
      <p class="text-sm text-gray-500">You've reached the end of the list. ✨</p>
    </div>
    }
  </main>

  <lib-button type="submit" class="fixed right-4 bottom-20 rounded-full" (click)="navigateToInvoicePage()">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2">
      <line x1="12" y1="5" x2="12" y2="19"></line>
      <line x1="5" y1="12" x2="19" y2="12"></line>
    </svg>
    <span>New Invoice</span>
  </lib-button>

  <lib-page-footer />
</div>
}
