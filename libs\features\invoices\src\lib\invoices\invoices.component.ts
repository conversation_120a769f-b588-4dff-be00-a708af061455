import { Component, inject, Signal, signal, WritableSignal, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { lastValueFrom, tap } from 'rxjs';

import { POSInvoice, PosOpeningEntry } from '@models';

import { ButtonComponent, PageFooterComponent, PageHeaderComponent, ProductCardComponent } from '@elements/ui';
import { NotificationService } from '@core';
import { AuthService, InvoiceService } from '@elevar-clients/api';

@Component({
  selector: 'lib-invoices',
  imports: [CommonModule, ProductCardComponent, PageHeaderComponent, PageFooterComponent, ButtonComponent],
  templateUrl: './invoices.component.html',
  styleUrls: ['./invoices.component.scss']
})
export class InvoicesComponent implements AfterViewInit {
  @ViewChild('scrollContainer', { static: false }) scrollContainer!: ElementRef;

  private readonly _router = inject(Router);
  private readonly _notificationService = inject(NotificationService);
  private readonly _invoiceService = inject(InvoiceService);

  // Local state management
  invoices = signal<POSInvoice[]>([]);
  isLoading = signal(false);
  isLoadingMore = signal(false);
  allInvoicesLoaded = signal(false);
  currentRange = signal({ start: 0, end: 0, total: 0 });

  // Constants
  private readonly PAGE_SIZE = 20;

  number = signal(0);
  searchQuery = signal<string>('');
  header = 'Sale Invoices';

  hasOpeningEntryToday: WritableSignal<boolean> = signal<boolean>(false);
  private readonly _mostRecentEntry: WritableSignal<PosOpeningEntry | null> = signal<PosOpeningEntry | null>(null);
  private readonly _loggedInUser = inject(AuthService);

  trackOpeningEntry$ = this._invoiceService.checkIfUserCanCreateInvoice(this._loggedInUser.loggedInUser()?.user_id as string).pipe(tap(res => {
    if (!res.isLastEntryClosed && !res.hasOpeningEntryToday) {
      this._mostRecentEntry.set(res.mostRecentEntry as PosOpeningEntry)
      return
    }
    this.hasOpeningEntryToday.set(res.hasOpeningEntryToday);
    this.checkIfUserCanRoute = !!res.isLastEntryClosed;
  }))

  signalInvoice!: Signal<number | undefined>;
  checkIfUserCanRoute = false;

  constructor() {
    this.loadInitialInvoices();
  }

  ngAfterViewInit() {
    this.setupScrollListener();
  }

  private setupScrollListener() {
    if (this.scrollContainer) {
      const element = this.scrollContainer.nativeElement;
      element.addEventListener('scroll', () => {
        this.handleScroll();
      });
    }
  }

  private handleScroll() {
    if (this.scrollContainer) {
      const element = this.scrollContainer.nativeElement;
      const { scrollTop, scrollHeight, clientHeight } = element;

      // Load more when user is near the bottom (within 100px)
      if (scrollHeight - scrollTop - clientHeight < 100 && !this.isLoadingMore() && !this.allInvoicesLoaded()) {
        this.loadMore();
      }
    }
  }

  private async loadInitialInvoices() {
    this.isLoading.set(true);
    try {
      const invoices = await lastValueFrom(this._invoiceService.getAllInvoices(0, this.PAGE_SIZE));
      this.invoices.set(invoices || []);
      this.updateCurrentRange(0, invoices?.length || 0);

      // Check if all invoices are loaded
      if ((invoices?.length || 0) < this.PAGE_SIZE) {
        this.allInvoicesLoaded.set(true);
      }
    } catch (error) {
      console.error('Error loading initial invoices:', error);
    } finally {
      this.isLoading.set(false);
    }
  }

  async loadMore() {
    if (this.isLoadingMore() || this.allInvoicesLoaded()) return;

    this.isLoadingMore.set(true);
    const currentCount = this.invoices().length;

    try {
      const newInvoices = await lastValueFrom(this._invoiceService.getAllInvoices(currentCount, this.PAGE_SIZE));

      if (newInvoices && newInvoices.length > 0) {
        // Append new invoices to existing ones
        const updatedInvoices = [...this.invoices(), ...newInvoices];
        this.invoices.set(updatedInvoices);
        this.updateCurrentRange(0, updatedInvoices.length);

        // Check if we've reached the end
        if (newInvoices.length < this.PAGE_SIZE) {
          this.allInvoicesLoaded.set(true);
        }
      } else {
        this.allInvoicesLoaded.set(true);
      }
    } catch (error) {
      console.error('Error loading more invoices:', error);
    } finally {
      this.isLoadingMore.set(false);
    }
  }

  private updateCurrentRange(start: number, end: number) {
    this.currentRange.set({ start: start + 1, end, total: end });
  }

  onSearchUpdated(sq: string) {
    this.searchQuery.set(sq.trim());
  }

  navigateToInvoiceDetail(name: string) {
    this._router.navigateByUrl(`/invoices/sales/${name}`);
  }

  getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'return':
        return 'text-red-500 bg-red-100'
      case 'paid':
        return 'text-green-500 bg-green-100'
      case 'new invoice':
        return 'text-blue-500 bg-blue-100'
      default:
        return 'text-gray-500 bg-gray-100'
    }
  }

  navigateToInvoicePage() {
    this._router.navigate(['/invoices/create']);
  }

  navigateToOpenEntries() {
    this._router.navigate(['/invoices/new-opening-entry']);
  }

  navigateToCloseEntries() {
    if (this._mostRecentEntry()) {
      this._router.navigate(['/invoices/new-closing-entry'], { state: { mostRecentEntry: this._mostRecentEntry() } });
    } else {
      this._notificationService.setWarning('No opening entry found', 'No opening entry found. Kindly contact Administrator.');
    }
  }
}
