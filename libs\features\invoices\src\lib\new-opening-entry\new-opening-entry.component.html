@if(isLoading) {
<lib-loader></lib-loader>
}

@if (submit$ | async; as data) { }

<div class="flex flex-col h-[calc(100vh-80px)] bg-gray-50">
  <lib-page-header [header]="header" [showArrowIcon]="true" [isSearchable]="false" />
  <main class="flex-1 overflow-auto p-4 space-y-4">

    <form class="space-y-4 mt-6" [formGroup]="formGroup" (ngSubmit)="onSubmit($event)">
      <div class="grid gap-6">
        <lib-elevar-inputs [control]="cashControl" inputId="cash_amount" label="Cash Opening Amount" type="number"
          placeholder="Enter cash opening amount">
        </lib-elevar-inputs>

        <lib-elevar-inputs [control]="mpesaControl" inputId="mpesa_amount" label="M-Pesa Opening Amount" type="number"
          placeholder="Enter M-Pesa opening amount">
        </lib-elevar-inputs>
      </div>

      <lib-button [buttonType]="formGroup.valid ? 'default' : 'disabled'" class="w-full block" type="submit">
        Submit Opening Entry
      </lib-button>
    </form>
  </main>
  <lib-page-footer>

  </lib-page-footer>
</div>
