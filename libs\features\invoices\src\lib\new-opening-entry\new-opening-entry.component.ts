import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ButtonComponent, PageHeaderComponent, PageFooterComponent, ElevarInputsComponent, LoaderComponent } from "@elements/ui";
import { BalanceDetail, PosOpeningEntry } from '@models';
import { catchError, EMPTY, Observable, tap } from 'rxjs';
import { Router } from '@angular/router';
import { NotificationService } from '@core';
import { AuthService, PosService } from '@elevar-clients/api';

@Component({
  selector: 'lib-new-opening-entry',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonComponent,
    PageFooterComponent,
    ElevarInputsComponent,
    PageHeaderComponent,
    LoaderComponent
  ],
  templateUrl: './new-opening-entry.component.html',
  styleUrl: './new-opening-entry.component.scss'
})
export class NewOpeningEntryComponent {
  header = 'POS Opening Entry';
  private readonly _fb = inject(FormBuilder);
  private readonly _posService = inject(PosService);
  private readonly _router = inject(Router);
  private readonly _notificationService = inject(NotificationService);
  isLoading = false;
  private readonly _loggedInUser = inject(AuthService);
  formGroup = this._fb.group({
    cash_amount: ['', [Validators.required, Validators.min(0)]],
    mpesa_amount: ['', [Validators.required, Validators.min(0)]]
  });

  submit$: Observable<PosOpeningEntry> = new Observable()

  get formIsValid(): boolean {
    return this.formGroup.valid;
  }

  onSubmit(event: Event): void {
    event.preventDefault();
    if (!this.formIsValid) return;

    this.isLoading = true;

    const balanceDetails: BalanceDetail[] = [
      {
        mode_of_payment: 'Cash',
        opening_amount: parseFloat(this.formGroup.get('cash_amount')?.value as string)
      },
      {
        mode_of_payment: 'M-Pesa',
        opening_amount: parseFloat(this.formGroup.get('mpesa_amount')?.value as string)
      }
    ];

    this.submit$ = this._posService.createOpeningEntry(balanceDetails, this._loggedInUser.loggedInUser()?.user_id as string).pipe(
      tap((res) => {
        this.isLoading = false;
        if (res) {
          this._notificationService.setSuccess('POS Opening entry', 'Creation of POS Opening entry was successful.');
          setTimeout(() => {
            this._notificationService.clearNotification();
          }, 3000);
          this.formGroup.reset();

          this.navigateToInvoicePage();
        } else {
          this._notificationService.setError('POS Opening entry', 'Creation of POS Opening entry failed.');
          setTimeout(() => {
            this._notificationService.clearNotification();
          }, 3000);
        }
      }),
      catchError(err => {
        console.log(err);
        this.isLoading = false;
        this._notificationService.setError('POS Opening entry', `Creation of POS Opening entry failed because of ${JSON.stringify(err)}`);
        return EMPTY;
      })
    );
  }

  get cashControl(): FormControl {
    return this.formGroup.get('cash_amount') as FormControl;
  }

  get mpesaControl(): FormControl {
    return this.formGroup.get('mpesa_amount') as FormControl;
  }

  navigateToInvoicePage() {
    this._router.navigate(['/invoices/create']);
  }

  onToastDismissed() {
    console.log('Toast dismissed');
  }
}
