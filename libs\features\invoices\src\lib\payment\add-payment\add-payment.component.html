@if (submit$ |async) {}
@if(customers$ | async) {}
@if(trackCustomerChanges$ | async) {}

<lib-page-header [header]="'Add Payment'" [showArrowIcon]="true" [isSearchable]="false" (performActionOnBack)="navigateToSales()" />

<main class="flex-grow p-4 overflow-y-auto">
  <div class="rounded-md overflow-hidden mb-4">
    <div class="overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
      @for (product of products; track product.item_code) {
        <lib-product-quantity-list [product]="product" (increment)="incrementQuantity(product)"
          (decrement)="decrementQuantity(product)" (remove)="removeProduct(product)" />
      }
    </div>
  </div>

  <div class="mt-8 space-y-4">
    <h3 class="text-lg font-semibold text-gray-800">COST</h3>
    <div class="space-y-2">
      <div class="flex justify-between">
        <span class="text-gray-600">Sub Total:</span>
        <span class="font-medium">Ksh. {{subtotal}}</span>
      </div>
    </div>
  </div>
  <div class="mt-6 bg-gray-50 p-4 rounded-xl">
    <div class="flex justify-between items-center">
      <span class="text-lg font-semibold">GRAND TOTAL</span>
      <span class="text-xl font-bold">KES. {{grandTotal}}</span>
    </div>
  </div>

  <div class="mt-6">
    <label for="userName" class="font-light text-base">Customer Name (Optional)</label>
    <lib-elevar-inputs [placeholder]="'Select Customer'" [type]="'selector'" [control]="formGroup.controls.userName"
      [inputId]="'userName'" [label]="'userName'" [options]="customerSelectOptions" [useOriginalObject]="true">
    </lib-elevar-inputs>
  </div>

  @if (!canSubmitPayment) {
    <div class="flex items-center gap-2 p-2 mb-2 rounded border border-[#ffcc00] bg-[#ffcc00]/20 text-[#332800] text-xs font-medium">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="#b28e00">
        <circle cx="12" cy="12" r="10" stroke="#b28e00" stroke-width="2" fill="#ffcc00"/>
        <path stroke="#332800" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M12 8v4m0 4h.01" />
      </svg>
      <span>{{ canSubmitPaymentReason }}</span>
    </div>
  }

  <div class="mt-6 space-y-4">
    <lib-button buttonType="notify-blue" class="w-full text-center border-rounded-lg block mb-3 custom-padding"
      (click)="openModal()">
      Pay Now
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
        class="size-6 ml-2.5">
        <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
      </svg>
    </lib-button>
    <lib-button [buttonType]="canSubmitPayment ? 'default': 'disabled'"
      class="w-full text-center block mb-3 custom-padding" (click)="submitInvoice()">SUBMIT</lib-button>
  </div>

  @if (showPaymentModal) {
    <lib-payment-modal [posInvoicePayments]="posInvoicePayments" [amount]="grandTotal" (save)="handlePaymentSave($event)"
      (close)="showPaymentModal = false" [submitInvoice]="submitInvoice.bind(this)" />
  }

  @if (isLoading) {
    <lib-loader></lib-loader>
  }
</main>
