import { Component, effect, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { catchError, debounceTime, distinctUntilChanged, filter, map, Observable, of, switchMap, tap } from 'rxjs';
import { PaymentModalComponent } from "../pay-modal/payment-modal.component";
import { ButtonComponent, ElevarInputsComponent, ProductQuantityListComponent, LoaderComponent, SelectOption, PageHeaderComponent } from '@elements/ui';
import { CartItem, Customer, POSInvoicePayment } from '@models';
import { InvoiceProxyService } from '../../services/invoice-proxy.service';
import { NotificationService, roundUpToShilling } from '@core';
import { CartService, CustomerService } from '@elevar-clients/api';
@Component({
  selector: 'lib-add-payment',
  imports: [
    CommonModule,
    FormsModule,
    ButtonComponent,
    ElevarInputsComponent,
    ReactiveFormsModule,
    PaymentModalComponent,
    ProductQuantityListComponent,
    LoaderComponent,
    PageHeaderComponent
  ],
  templateUrl: './add-payment.component.html',
  styleUrl: './add-payment.component.scss'
})
export class AddPaymentComponent implements OnInit {
  private readonly route = inject(Router);
  private readonly cartService = inject(CartService);
  private readonly _invoiceProxyService = inject(InvoiceProxyService);
  private readonly _notificationService = inject(NotificationService);
  private readonly _customerService = inject(CustomerService);
  showPaymentModal = false;

  products: CartItem[] = [];

  readonly initializeData = effect(() => {
    this.products = this.cartService.items();
    if (this.products.length === 0 || !this._invoiceProxyService.posProfile) {
      this.navigateToSales();
    }
  });

  customers$: Observable<SelectOption[]> = this._customerService.getCustomersByGroup().pipe(map(res => res.map(customer => ({ label: customer.customer_name, value: customer.customer_name, originalObject: customer }))),
   tap(res => this.customerSelectOptions = res),
   tap(res => this.initialCustomer = res.find(customer => customer.value === this._invoiceProxyService.posProfile?.customer)?.originalObject as Customer | null),
   tap(res => this.formGroup.get('userName')?.setValue(this.initialCustomer))
  );

  initialCustomer:Customer | null = null;
  customerSelectOptions: SelectOption[] = [];

  formGroup = new FormGroup({
    userName: new FormControl(this.initialCustomer, [Validators.required]),
  });

  submit$: Observable<any> = new Observable();
  posInvoicePayments: POSInvoicePayment[] = [];
  isLoading = false;
  canSubmitPayment = false;
  trackCustomerChanges$ = this.formGroup.get('userName')?.valueChanges
    .pipe(
      debounceTime(500),
      distinctUntilChanged(),
      filter(customer => customer !== null),
      switchMap(customer => this._invoiceProxyService.updateCartItemsWithNewPriceList(customer.default_price_list))
    );

  ngOnInit() {
    // Check if cart is empty initially
    if (this.cartService.items().length === 0) {
      this.navigateToSales();
      return;
    }
    this.updateProducts();
  }

  get subtotal(): number {
    const total = this.products.reduce((sum, product) =>
      sum + ((product.price ?? 0) * product.quantity), 0);
    return roundUpToShilling(total);
  }

  handlePaymentSave(data: POSInvoicePayment[]) {
    this.showPaymentModal = false;
    this.posInvoicePayments = data;
    this._checkIfUserCanSubmitPayment();
  }

  get grandTotal(): number {
    return this.subtotal;
  }

  isSelector(formControl: string) {
    return formControl.includes('selected');
  }

  isPassword(formControl: string) {
    return formControl.includes('password');
  }

  openModal() {
    this.showPaymentModal = true;
  }

  navigateToSales() {
    this.route.navigate(['/invoices/sales']);
  }

  a = roundUpToShilling(this.posInvoicePayments.reduce((acc, curr) => acc + curr.amount, 0))

  private _checkIfUserCanSubmitPayment() {
    const totalPaid = roundUpToShilling(this.posInvoicePayments.reduce((acc, curr) => acc + curr.amount, 0));
    this.canSubmitPayment = totalPaid === this.grandTotal;
  }

  incrementQuantity(product: CartItem) {
    if (this.cartService.hasItem(product.item_code)) {
      const currentQuantity = product.quantity;
      const newQuantity = currentQuantity + 0.001; // Increment by 0.001
      const formattedQuantity = Math.round(newQuantity * 1000) / 1000; // Ensure 3 decimal places

      this.cartService.updateItem({
        ...product,
        quantity: formattedQuantity
      });
    } else {
      this.cartService.addItem(product, 0.001);
    }
  }

  //TODO: Track changes in the form group
  //1. When the customer changes, we need to update the pricelist.
  //2. when we update the price list, the items in the cart need to be updated with the new price list.
  //From the proxy service use the item service to fetch the new price list,
  // Go throiugh the cart and upodate the prices,
  // if item is not found in the new price list, remove it from the cart.
  //alert user the item has been removed from the cart due to no longer being available in the new price list.
  //update cart items with the new price list.


  decrementQuantity(product: CartItem) {
    const currentQuantity = product.quantity;
    const newQuantity = currentQuantity - 0.001; // Decrement by 0.001

    if (newQuantity >= 0.001) {
      // If quantity would remain above minimum, update it
      const formattedQuantity = Math.round(newQuantity * 1000) / 1000; // Ensure 3 decimal places
      this.cartService.updateItem({
        ...product,
        quantity: formattedQuantity
      });
    } else if (newQuantity < 0.001) {
      // If quantity would go below minimum, remove the item
      this.removeProduct(product);
    }
  }

  removeProduct(product: CartItem) {
    this.cartService.removeItem(product.item_code);
    if (this.products.length === 1) {
      this.navigateToSales();
    }
  }

  private updateProducts() {
    this.products = this.cartService.items();
  }

  submitInvoice() {
    this.isLoading = true;
    this.submit$ =
      this._invoiceProxyService.createInvoice({ payments: this.posInvoicePayments, customer: this.formGroup.get('userName')?.value?.customer_name ?? null }).pipe(tap(res => {
        this.isLoading = false;
        if (res) {
          this.cartService.clearCart();
          this.route.navigate(['/invoices']);
        }
      }),
        catchError(error => {
          this.isLoading = false;
          this._notificationService.setError('Error submitting invoice', 'There was an error submitting the invoice. Please try again.');
          console.error("Error submitting invoice", JSON.stringify(error))
          return of(null);
        })
      )
  }

  get canSubmitPaymentReason(): string {
    if (this.posInvoicePayments.length === 0) {
      return 'Please add at least one payment.';
    }
    if (this.posInvoicePayments.some(payment => !payment.mode_of_payment)) {
      return 'Please select a payment method for all payments.';
    }
    if (this.isAnyPaymentEditing) {
      return 'Please save all payment entries before submitting.';
    }
    const totalPaid = roundUpToShilling(this.posInvoicePayments.reduce((acc, curr) => acc + curr.amount, 0));
    if (totalPaid < this.grandTotal) {
      return 'Total paid must be equal to or greater than the grand total.';
    }
    return '';
  }

  get isAnyPaymentEditing(): boolean {
    return this.posInvoicePayments.some((p: any) => p.isEditing);
  }
}
