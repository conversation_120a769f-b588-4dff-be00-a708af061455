<div class="fixed inset-0 z-50 flex items-center justify-center p-4">
  <div class="fixed inset-0 bg-black/50 backdrop-blur-sm"></div>

  <div class="bg-white rounded-lg shadow-xl w-full max-w-md z-10 overflow-y-auto max-h-[90vh]">
    <div class="flex justify-between items-center p-4 border-b">
      <h2 class="text-lg font-semibold">
        Recieve: <span class="text-green-500"> KSH. {{ amount }}</span>
      </h2>
      <button class="p-2 hover:bg-gray-100 rounded-full" (click)="closeEvent.emit()">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
            clip-rule="evenodd" />
        </svg>
      </button>
    </div>

    <div class="p-4 space-y-6">
      <div class="space-y-4">
        <div *ngFor="let payment of payments; let i=index; trackBy: trackByIndex" class="border rounded-lg">
          <button class="flex items-center justify-between w-full p-3 cursor-pointer bg-gray-50"
            (click)="toggleAccordion(i)" (keydown.enter)="toggleAccordion(i)" type="button">
            <h3 *ngIf=" payment.modeOfPayment?.length" class="text-sm font-medium">
              {{ payment.modeOfPayment | titlecase }} - KSH. {{ payment.amount || 0 }}
            </h3>
            <svg class="h-5 w-5 transform transition-transform" [ngClass]="{'rotate-180': payment.isOpen}"
              xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clip-rule="evenodd" />
            </svg>
          </button>

          <div *ngIf="payment.isOpen" class="p-3 space-y-3">
            <div>
              <label *ngIf="payment.isEditing" [for]="'mode-' + i" class="block text-sm font-medium mb-1">
                Payment Method
              </label>
              <lib-template-driven-input [(value)]="payment.modeOfPayment" [inputId]="'mode-' + i"
                [label]="'Payment Method'" [type]="'selector'" [options]="paymentOptions" [name]="'modeOfPayment-' + i"
                [disabled]="!payment.isEditing" [placeholder]="payment.isEditing ? 'Select Payment Method' : undefined"
                required></lib-template-driven-input>
            </div>

            <div>
              <label *ngIf="payment.isEditing" [for]="'amount-' + i" class="block text-sm font-medium mb-1">
                Amount
              </label>
              <lib-template-driven-input [(value)]="payment.amount" [inputId]="'amount-' + i" [label]="'Amount'"
                [type]="'number'" [name]="'amount-' + i" [disabled]="!payment.isEditing"
                [placeholder]="payment.isEditing ? 'Enter Amount' : undefined" required></lib-template-driven-input>
            </div>

            <div class="flex" [ngClass]="payments.length === 1 ? 'justify-end' : 'justify-between'">

              <button *ngIf="payments.length > 1" type="button" (click)="removePayment(i)"
                class="text-red-500 outline outline-1 rounded p-1 hover:text-red-700 text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>

              <button type="button" (click)="toggleEdit(i)"
                [disabled]="payment.isEditing && (!payment.amount || payment.amount === 0)"
                class="text-blue-600 hover:text-blue-800 text-sm outline outline-1  rounded disabled:text-gray-400 p-1">
                save payment entry
              </button>
            </div>
          </div>
        </div>

        <button type="button" (click)="addPayment()" [disabled]="canProceed || isAnyPaymentEditing"
          class="w-full text-blue-600 hover:text-blue-800 text-sm disabled:text-gray-400">
          + Add Payment Method
        </button>
      </div>

      <div class="space-y-2 text-sm">
        <div class="flex justify-between">
          <span>Total Paid:</span>
          <span class="font-medium">KSH. {{ totalPaid }}</span>
        </div>
        <div class="flex justify-between">
          <span>Remaining:</span>
          <span class="font-medium" [ngClass]="{'text-red-600': amount - totalPaid > 0}">
            KSH. {{ amount - totalPaid > 0 ? amount - totalPaid : 0 }}
          </span>
        </div>
        <div class="flex justify-between" *ngIf="change > 0">
          <span>Change:</span>
          <span class="font-medium text-green-600">KSH. {{ change }}</span>
        </div>
      </div>

      <lib-button [buttonType]="canProceed && !isAnyPaymentEditing ? 'default' : 'disabled'" (click)="onProceed()"
        class="w-full block">
        PROCEED
      </lib-button>
    </div>
  </div>
</div>
