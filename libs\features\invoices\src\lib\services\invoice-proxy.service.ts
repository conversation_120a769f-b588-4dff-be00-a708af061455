import { inject, Injectable } from '@angular/core';
import { NotificationService } from '@core';
import { map, Observable, tap } from 'rxjs';

import { POSInvoicePayment, POSInvoiceRequest } from '@models';
import { CartService, ItemService, PosProfileService, PosService } from '@elevar-clients/api';

@Injectable({ providedIn: 'root' })
export class InvoiceProxyService {
  private readonly _posService = inject(PosService)
  private readonly _posProfileService = inject(PosProfileService);
  private readonly _notificationService = inject(NotificationService);
  private readonly _cartService = inject(CartService);
  private readonly _itemService = inject(ItemService);

  get posProfile() {
    return this._posProfileService.posProfile();
  }

  createInvoice(input: {
    payments: POSInvoicePayment[],
    customer: string | null
  }) {
    const posProfile = this._posProfileService.posProfile();
    const posItems = this._cartService.items().map(i => {
      return {
        item_code: i.item_code,
        qty: i.quantity
      }
    })
    if (!posProfile) {
      this._notificationService.setError('Invalid POS Profile', 'The POS Profile is required.')
      throw new Error('POS Profile not found')
    }
    const reqObject: POSInvoiceRequest = {
      is_pos: 1,
      company: posProfile.company,
      selling_price_list: posProfile.selling_price_list,
      customer: input.customer ?? posProfile.customer,
      items: posItems,
      payments: input.payments,
      "docstatus": 1
    }

    return this._posService.createPOSInvoice(reqObject).pipe(tap(res => {
      this._cartService.clearCart()
      this._notificationService.setSuccess('Invoice Created', 'Invoice has been created successfully.')
    }))
  }

  updateCartItemsWithNewPriceList(priceList: string): Observable<boolean> {
    const items = this._cartService.items();
    return this._itemService.getItemPrices(0, 20, priceList).pipe(
      map(prices => {
        const priceMap = new Map<string, number>();
        prices.forEach(price => priceMap.set(price.item_code, price.price_list_rate));
        const updatedCartItems = items.map(item => {
          const price = priceMap.get(item.item_code);
          if (price) {
            return { ...item, price };
          }
          return null;
        });

        const filteredItems = updatedCartItems.filter(item => item !== null);
        this._cartService.updateCart(filteredItems);
        return filteredItems.length > 0;
      })
    )
  }



}