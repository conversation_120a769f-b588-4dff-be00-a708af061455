@if(invoice$ | async; as invoice) {
<div class="flex flex-col h-[calc(100vh-80px)] bg-gray-50">
  <lib-page-header [header]="invoice.name" [showArrowIcon]="true" [isSearchable]="false" />

  <main class="flex-grow p-4 overflow-y-auto space-y-4">
    <!-- Invoice Header Section as Accordion -->
    <div class="rounded-md overflow-hidden border border-gray-200 shadow-sm">
      <button (click)="toggleAccordion('header')" class="flex justify-between items-center w-full p-4"
        [ngClass]="accordionState.header ? 'bg-primary-hoverGreen text-white' : 'bg-primary-greenLight text-white'"
        type="button">
        <h2 class="text-base font-medium">Invoice Details</h2>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transition-transform"
          [ngClass]="accordionState.header ? 'rotate-180' : ''" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      @if(accordionState.header) {
      <div class="bg-white p-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label for="customer" class="block text-sm font-light text-gray-700 mb-1">Customer</label>
            <p id="customer" class="text-sm font-medium text-gray-900">{{ invoice.customer }}</p>
          </div>
          <div>
            <label for="company" class="block text-sm font-light text-gray-700 mb-1">Company</label>
            <p class="text-sm font-medium text-gray-900">{{ invoice.company }}</p>
          </div>
          <div>
            <label for="pos_profile" class="block text-sm font-light text-gray-700 mb-1">POS Profile</label>
            <p id="pos_profile" class="text-sm font-medium text-gray-900">{{ invoice.pos_profile }}</p>
          </div>

          <div>
            <label for="set_warehouse" class="block text-sm font-light text-gray-700 mb-1">Warehouse</label>
            <p id="set_warehouse" class="text-sm font-medium text-gray-900">{{ invoice.set_warehouse }}</p>
          </div>
        </div>
      </div>
      }
    </div>


    <!-- Items Section -->
    <div class="rounded-md overflow-hidden border border-gray-200 shadow-sm">
      <button (click)="toggleAccordion('items')" class="flex justify-between items-center w-full p-4"
        [ngClass]="accordionState.items ? 'bg-primary-hoverGreen text-white' : 'bg-primary-greenLight text-white'"
        type="button">
        <div class="flex items-center">
          <h2 class="text-base font-medium">Items</h2>
          @if(invoice.items && invoice.items.length > 0) {
          <span class="ml-2 text-xs bg-white text-primary-hoverGreen rounded-full px-2 py-0.5">
            {{ invoice.items.length }}
          </span>
          }
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transition-transform"
          [ngClass]="accordionState.items ? 'rotate-180' : ''" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      @if(accordionState.items) {
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead>
            <tr class="bg-primary-greenLight">
              <th class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-medium text-white w-1/4">
                Item Name
              </th>
              <th class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-medium text-white w-1/4">
                Qty
              </th>
              <th class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-medium text-white w-1/4">
                Price
              </th>
              <th class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-medium text-white w-1/4">
                Amount
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 bg-white">
            @if (!invoice.items || invoice.items.length === 0) {
            <tr>
              <td colspan="4" class="px-4 py-8 text-center text-sm text-gray-500">
                <div class="flex flex-col items-center justify-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <span>No items in invoice</span>
                </div>
              </td>
            </tr>
            }
            @for(item of invoice.items; track item.item_code) {
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="whitespace-nowrap px-2 py-4 text-sm">
                <div class="flex flex-col">
                  <span class="font-medium text-gray-900">{{ item.item_code | titlecase }}</span>
                </div>
              </td>
              <td class="whitespace-nowrap px-2 py-4 text-sm text-center">
                <div class="flex items-center justify-center">
                  <span
                    class="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700">
                    {{ item.qty }}
                  </span>
                </div>
              </td>
              <td class="whitespace-nowrap px-2 py-4 text-sm text-gray-600 text-center">
                <span
                  class="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
                  {{ invoice.currency }} {{ item.rate }}
                </span>
              </td>
              <td class="whitespace-nowrap px-2 py-4 text-sm text-gray-600 text-center">
                {{ item.amount | currency:invoice.currency }}
              </td>
            </tr>
            }
          </tbody>
        </table>
      </div>
      }
    </div>

    <!-- Payments Section -->
    <div class="rounded-md overflow-hidden border border-gray-200 shadow-sm">
      <button (click)="toggleAccordion('payments')" class="flex justify-between items-center w-full p-4"
        [ngClass]="accordionState.payments ? 'bg-primary-hoverGreen text-white' : 'bg-primary-greenLight text-white'"
        type="button">
        <div class="flex items-center">
          <h2 class="text-base font-medium">Payments</h2>
          @if(invoice.payments && invoice.payments.length > 0) {
          <span class="ml-2 text-xs bg-white text-primary-hoverGreen rounded-full px-2 py-0.5">
            {{ invoice.payments.length }}
          </span>
          }
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transition-transform"
          [ngClass]="accordionState.payments ? 'rotate-180' : ''" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      @if(accordionState.payments) {
      <div class="max-h-48 overflow-y-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-primary-greenLight sticky top-0">
            <tr>
              <th class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-medium text-white w-1/2">
                id
              </th>
              <th class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-medium text-white w-1/2">
                Payment Method
              </th>
              <th class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-medium text-white w-1/2">
                Amount
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 bg-white">
            @if (!invoice.payments || invoice.payments.length === 0) {
            <tr>
              <td colspan="2" class="px-4 py-8 text-center text-sm text-gray-500">
                <div class="flex flex-col items-center justify-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span>No payments recorded</span>
                </div>
              </td>
            </tr>
            }
            @for(payment of invoice.payments; track payment.name) {
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="whitespace-nowrap px-2 py-4 text-sm">
                <span class="font-medium text-gray-900">{{ payment.name }}</span>
              </td>
              <td class="whitespace-nowrap px-2 py-4 text-sm">
                <span class="font-medium text-gray-900">{{ payment.mode_of_payment }}</span>
              </td>
              <td class="whitespace-nowrap px-2 py-4 text-sm text-gray-600">
                <span
                  class="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
                  {{ payment.amount | currency:invoice.currency }}
                </span>
              </td>
            </tr>
            }
          </tbody>
        </table>
      </div>
      }
    </div>

    <!-- Summary Section -->
    <div class="rounded-md overflow-hidden border border-gray-200 shadow-sm">
      <button (click)="toggleAccordion('summary')" class="flex justify-between items-center w-full p-4"
        [ngClass]="accordionState.summary ? 'bg-primary-hoverGreen text-white' : 'bg-primary-greenLight text-white'"
        type="button">
        <h2 class="text-base font-medium">Summary</h2>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transition-transform"
          [ngClass]="accordionState.summary ? 'rotate-180' : ''" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      @if(accordionState.summary) {
      <div class="p-4 space-y-2 bg-white">
        @if(invoice.items && invoice.items.length > 0) {
        <div class="flex justify-between items-center">
          <span class="text-sm font-light text-gray-700">Total Items:</span>
          <span class="text-sm font-medium text-gray-900">{{ invoice.items.length }}</span>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-sm font-light text-gray-700">Total Amount:</span>
          <span class="text-sm font-medium text-gray-900">{{ calculateTotal(invoice.items) | currency:invoice.currency
            }}</span>
        </div>
        }
        @if(invoice.payments && invoice.payments.length > 0) {
        <div class="flex justify-between items-center">
          <span class="text-sm font-light text-gray-700">Total Payments:</span>
          <span class="text-sm font-medium text-gray-900">{{ calculateTotalPayments(invoice.payments) |
            currency:invoice.currency }}</span>
        </div>
        }
      </div>
      }
    </div>
    @if(invoice.is_return !== 1 && invoice.status === 'Paid'){
      <lib-button #showModalText buttonType="notify-blue" class="fixed right-4 bottom-20 rounded-full">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
          <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15M12 9l-3 3m0 0 3 3m-3-3h12.75" />
        </svg>
        &nbsp;
        Return
      </lib-button>

      @defer (on interaction(showModalText)) {
        <lib-elevar-modal [isOpen]=true [title]="'Return Invoice'" [description]="'Are you sure you want to return to the invoice?'"
          [type]="'warning'" [showConfirmButton]="true" (confirmButtonClicked)="confirmButtonClicked($event, invoice)">
        </lib-elevar-modal>
      }

      <lib-button #showModalText2 buttonType="notify-green" class="fixed left-4 bottom-20 rounded-full">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
          <path stroke-linecap="round" stroke-linejoin="round" d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.************.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z"  />
        </svg>
        &nbsp;
        Print
      </lib-button>

      @defer (on interaction(showModalText2)) {
        <lib-elevar-modal [isOpen]=true [title]="'Print Invoice'" [description]="'Are you sure you want to print to the invoice?'"
          [type]="'warning'" [showConfirmButton]="true" (confirmButtonClicked)="printButtonClicked($event, invoice)">
        </lib-elevar-modal>
      }
    }

    @if(invoice.docstatus === 0) {
      <div class="mt-6 space-y-4 p-4">
        <lib-button [buttonType]="loading ? 'disabled' : 'default'"
          class="w-full text-center block mb-3 custom-padding"
          [disabled]="loading"
          (click)="submitInvoice(invoice)">
          SUBMIT
        </lib-button>
      </div>
    }

  </main>
  <lib-page-footer />
</div>
}



