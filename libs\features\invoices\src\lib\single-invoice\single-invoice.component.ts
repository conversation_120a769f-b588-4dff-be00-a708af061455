import { Component, inject, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule, CurrencyPipe, TitleCasePipe } from '@angular/common';

import { NotificationService, roundUpToShilling } from '@core';
import { InvoiceDetailItem, InvoiceDetailPayment } from '@models';

import { PageHeaderComponent, PageFooterComponent, ButtonComponent, ToastComponent, ElevarModalComponent } from '@elements/ui';
import { InvoiceService, PosService } from '@elevar-clients/api';

@Component({
  selector: 'lib-single-invoice',
  standalone: true,
  imports: [CommonModule, PageHeaderComponent, ButtonComponent, PageFooterComponent,ElevarModalComponent, CurrencyPipe, TitleCasePipe],
  templateUrl: './single-invoice.component.html',
  styleUrl: './single-invoice.component.scss'
})
export class SingleInvoiceComponent {
  @ViewChild('loginToast') toastComponent!: ToastComponent;

  private readonly _route = inject(ActivatedRoute);
  private readonly _invoiceService = inject(InvoiceService);
  private readonly router = inject(Router);
  private readonly _posService = inject(PosService)
  private readonly _notificationService = inject(NotificationService);

  invoiceName = this._route.snapshot.params['id'];
  invoice$ = this._invoiceService.getInvoiceDetail(this.invoiceName);

  // Accordion states
  accordionState = {
    header: true,
    items: true,
    payments: true,
    summary: true
  };

  loading = false;

  /**
   * Toggle the specified accordion section
   * @param section The accordion section to toggle
   */
  toggleAccordion(section: 'header' | 'items' | 'payments' | 'summary'): void {
    this.accordionState[section] = !this.accordionState[section];
  }

  /**
   * Calculate the total amount from invoice items
   */
  calculateTotal(items: InvoiceDetailItem[]): number {
    const total = items.reduce((total, item) => total + item.amount, 0);
    return roundUpToShilling(total);
  }

  /**
   * Calculate the total amount from invoice payments
   */
  calculateTotalPayments(payments: InvoiceDetailPayment[]): number {
    const total = payments.reduce((total, payment) => total + payment.amount, 0);
    return roundUpToShilling(total);
  }

  confirmButtonClicked(confirmed: boolean, invoice: any): void {
    if (!confirmed || !invoice) return;

    const transformItems = (items: { item_code: string; qty: number }[] = []) =>
      items.map(item => ({
        item_code: item.item_code,
        qty: -Math.abs(item.qty)
      }));

    const transformPayments = (payments: { mode_of_payment: string; amount: number }[] = []) => {
      return payments.map(payment => ({
        mode_of_payment: payment.mode_of_payment,
        amount: -Math.abs(payment.amount)
      }));
    };

    const originalPayments = invoice.payments ?? [];
    const paid_amount = roundUpToShilling(originalPayments.reduce(
      (sum: number, p: { amount: number }) => sum + p.amount,
      0
    ));

    const payload = {
      is_pos: invoice.is_pos,
      customer: invoice.customer,
      company: invoice.company,
      paid_amount: -Math.abs(paid_amount),
      is_return: 1,
      return_against: invoice.name,
      items: transformItems(invoice.items ?? []),
      payments: transformPayments(originalPayments)
    };

    this._posService.createPOSInvoice(payload).subscribe(() => {
      this._notificationService.setSuccess('Success', 'Your invoice return request has been submitted successfully.');
      this.router.navigate(['/invoices/sales']);
    });
  }

  printButtonClicked(confirmed: boolean, invoice: any): void {
    if (!confirmed || !invoice) return;
    //open print view using default broswer to print from the ERP Using {{base_url}}/printview?doctype=POS%20Invoice&name={{inv_name}}&trigger_print=1&format=POS%20Invoice&no_letterhead=0&letterhead=Elevar&_lang=en
    const url = `https://elevar-develop.frappe.cloud/printview?doctype=POS%20Invoice&name=${invoice.name}&trigger_print=1&format=POS%20Invoice&no_letterhead=0&letterhead=Elevar&_lang=en`;
    window.open(url, '_blank');
    this.router.navigate(['/invoices/sales']);

  }

  submitInvoice(invoice: any) {
    if (!invoice) return;
    this.loading = true;
    this._invoiceService.submitInvoice(invoice.name).subscribe({
      next: () => {
        this._notificationService.setSuccess('Submitted', 'Invoice submitted successfully.');
        this.invoice$ = this._invoiceService.getInvoiceDetail(this.invoiceName);
        this.loading = false;
      },
      error: () => {
        this._notificationService.setError('Error', 'Failed to submit invoice.');
        this.loading = false;
      }
    });
  }
}
