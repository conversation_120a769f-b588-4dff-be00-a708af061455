{"name": "reports", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/features/reports/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/features/reports/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/features/reports/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/features/reports/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/features/reports/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}