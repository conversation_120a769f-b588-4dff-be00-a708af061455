import { Route } from '@angular/router';

export const routes: Route[] = [
  {
    path: '',
    loadComponent: () => import('./reports/reports.component').then(c => c.ReportsComponent),
    title: 'Elevar - Reports'
  },
  {
    path: 'transaction/:name',
    loadComponent: () => import('./single-transaction/single-transaction.component').then(c => c.SingleTransactionComponent),
    title: 'Elevar - Transaction Details'
  },
  {
    path: '**',
    redirectTo: '/reports',
    pathMatch: 'full'
  }
]
