<div class="flex flex-col h-[calc(100vh-80px)] bg-gray-50">
  <lib-page-header [header]="header"  [showArrowIcon]="false" [isSearchable]="true" [placeholder]="'Search Transaction'" (search)="onSearch($event)"/>
  <main class="flex-1 overflow-auto p-4 space-y-4 ">

    <div class="space-y-2">
      @for(statement of filteredTransactions; track statement.name){
      <lib-mpesa-details-card [statement]="statement" />
      }
    </div>

  </main>
  <lib-page-footer />

</div>
