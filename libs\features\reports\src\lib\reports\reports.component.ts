import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@elements/ui';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NotificationService } from '@core';
import { toast } from 'ngx-sonner';
import { MpesaTransactionService } from '@elevar-clients/api';
import { MpesaTransaction } from '@models';

@Component({
  selector: 'lib-reports',
  imports: [CommonModule, SharedModule],
  templateUrl: './reports.component.html',
  styleUrl: './reports.component.scss',
})
export class ReportsComponent implements OnInit {
  protected readonly toast = toast;
  private readonly _notificationService = inject(NotificationService);
  private readonly mpesaTransactionService = inject(MpesaTransactionService);

  header = 'Transactions';

  formGroup = new FormGroup({
    search: new FormControl('', [Validators.required]),
    selector: new FormControl('', [Validators.required]),
  });

  get searchControl(): FormControl {
    return this.formGroup.get('search') as FormControl;
  }

  get selectControl(): FormControl {
    return this.formGroup.get('selector') as FormControl;
  }
  isSimulating = true;
  searchQuery = '';

  transactions: MpesaTransaction[] = [];
  filteredTransactions: MpesaTransaction[] = [];
  loading = false;

  ngOnInit(): void {
    this.fetchTransactions();
  }

  fetchTransactions() {
    this.loading = true;
    this.mpesaTransactionService.getTransactions({
      filters: [],
      order_by: 'create_time',
      fields: [
        'name',
        'trans_id',
        'trans_amount',
        'business_shortcode',
        'first_name',
        'msisdn'
      ]
    }).subscribe({
      next: (data) => {
        this.transactions = data;
        this.filteredTransactions = data;
        this.loading = false;
      },
      error: (err) => {
        this._notificationService.setError('Failed to load transactions', 'An error occurred while fetching Mpesa transactions.');
        this.loading = false;
      }
    });
  }

  onSearch(query: string) {
    const q = query.toLowerCase();
    this.filteredTransactions = this.transactions.filter(t =>
      t.first_name.toLowerCase().includes(q) ||
      t.trans_id.toLowerCase().includes(q) ||
      t.business_shortcode.toLowerCase().includes(q) ||
      t.name.toLowerCase().includes(q)
    );
  }

  toggleSimulation(): void {
    this.isSimulating = !this.isSimulating;
  }

  setMessage(){
    if (this.transactions.length > 0) {
      const t = this.transactions[0];
      toast.message(t.trans_id, {
        description: `${t.first_name}, KES ${t.trans_amount}`,
      });
    }
  }
}
