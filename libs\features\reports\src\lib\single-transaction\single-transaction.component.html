@if(transaction$ | async; as transaction) {
<div class="flex flex-col h-[calc(100vh-80px)] bg-gray-50">
  <lib-page-header [header]="transaction.name" [showArrowIcon]="true" [isSearchable]="false" />
  <main class="flex-grow p-4 overflow-y-auto space-y-4">
    <div class="rounded-md border border-gray-200 shadow-sm bg-white p-6">
      <h2 class="text-lg font-semibold mb-4">Transaction Details</h2>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <div class="text-xs text-gray-500 mb-1">Transaction ID</div>
          <div class="text-sm font-medium text-gray-900">{{ transaction.trans_id }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">Amount</div>
          <div class="text-sm font-medium text-green-700">KES {{ transaction.trans_amount | number:'1.0-0' }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">Shortcode</div>
          <div class="text-sm font-medium text-gray-900">{{ transaction.business_shortcode }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">First Name</div>
          <div class="text-sm font-medium text-gray-900">{{ transaction.first_name }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">Reference</div>
          <div class="text-sm font-medium text-gray-900">{{ transaction.name }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">Owner</div>
          <div class="text-sm font-medium text-gray-900">{{ transaction.owner }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">Company</div>
          <div class="text-sm font-medium text-gray-900">{{ transaction.erp_company }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">Transaction Type</div>
          <div class="text-sm font-medium text-gray-900">{{ transaction.transaction_type }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">Allocation Status</div>
          <div class="text-sm font-medium text-gray-900">{{ transaction.allocation_status }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">Created</div>
          <div class="text-sm font-medium text-gray-900">{{ formatDateTime(transaction.creation) }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">Modified</div>
          <div class="text-sm font-medium text-gray-900">{{ formatDateTime(transaction.modified) }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">Doc Status</div>
          <div class="text-sm font-medium text-gray-900">{{ transaction.docstatus }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">Org Account Balance</div>
          <div class="text-sm font-medium text-gray-900">{{ transaction.org_account_balance }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">Bill Ref Number</div>
          <div class="text-sm font-medium text-gray-900">{{ transaction.bill_ref_number }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">Trans Time</div>
          <div class="text-sm font-medium text-gray-900">{{ formatTransTime(transaction.trans_time) }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">Create Time</div>
          <div class="text-sm font-medium text-gray-900">{{ formatCreateTime(transaction.create_time, transaction.creation) }}</div>
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">Doctype</div>
          <div class="text-sm font-medium text-gray-900">{{ transaction.doctype }}</div>
        </div>
      </div>
    </div>
  </main>
  <lib-page-footer />
</div>
}
