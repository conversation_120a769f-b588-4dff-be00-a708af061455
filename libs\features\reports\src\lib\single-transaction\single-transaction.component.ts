import { Component, OnInit, inject } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { MpesaTransactionService } from '@elevar-clients/api';
import { MpesaTransactionDetails } from '@models';
import { Observable, of, switchMap } from 'rxjs';
import { PageHeaderComponent } from '@elements/ui';
import { PageFooterComponent } from '@elements/ui';

@Component({
  selector: 'lib-single-transaction',
  standalone: true,
  imports: [CommonModule, PageHeaderComponent, PageFooterComponent],
  providers: [DatePipe],
  templateUrl: './single-transaction.component.html',
  styleUrl: './single-transaction.component.scss',
})
export class SingleTransactionComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly mpesaTransactionService = inject(MpesaTransactionService);
  private readonly datePipe = inject(DatePipe);

  transaction$!: Observable<MpesaTransactionDetails>;
  loading = true;

  ngOnInit(): void {
    this.transaction$ = this.route.paramMap.pipe(
      switchMap(params => {
        const name = params.get('name');
        if (!name) return of(null as any);
        this.loading = true;
        return this.mpesaTransactionService.getTransactionByName(name) as Observable<MpesaTransactionDetails>;
      })
    );
  }

  formatDateTime(dateStr: string | null | undefined): string {
    if (!dateStr) return 'N/A';
    // Handles 'YYYY-MM-DD HH:mm:ss.SSSSSS' or 'YYYY-MM-DD HH:mm:ss' format
    const date = new Date(dateStr.replace(' ', 'T'));
    if (isNaN(date.getTime())) return dateStr || 'N/A';
    return this.datePipe.transform(date, 'medium') || dateStr;
  }

  formatTransTime(transTime: string | null | undefined): string {
    // Handles 'YYYYMMDDHHmmss' format
    if (!transTime || transTime.length !== 14) return transTime || 'N/A';
    const year = +transTime.slice(0, 4);
    const month = +transTime.slice(4, 6) - 1;
    const day = +transTime.slice(6, 8);
    const hour = +transTime.slice(8, 10);
    const min = +transTime.slice(10, 12);
    const sec = +transTime.slice(12, 14);
    const date = new Date(year, month, day, hour, min, sec);
    if (isNaN(date.getTime())) return transTime;
    return this.datePipe.transform(date, 'medium') || transTime;
  }

  formatCreateTime(createTime: string | null | undefined, creationDate?: string): string {
    // create_time is usually 'HH:mm:ss.SSSSSS', combine with creation date if available
    if (!createTime) return 'N/A';
    if (creationDate) {
      const datePart = creationDate.split(' ')[0];
      const dateTimeStr = `${datePart}T${createTime.split('.')[0]}`;
      const date = new Date(dateTimeStr);
      if (isNaN(date.getTime())) return createTime;
      return this.datePipe.transform(date, 'mediumTime') || createTime;
    }
    return createTime;
  }
}
