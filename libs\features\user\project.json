{"name": "user", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/features/user/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/features/user/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/features/user/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/features/user/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/features/user/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}