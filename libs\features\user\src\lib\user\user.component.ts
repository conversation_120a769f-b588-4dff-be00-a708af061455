import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@elements/ui';
import { AuthService } from '@elevar-clients/api';
import { decryptObject, ENCRYPTION_KEY } from '@core';

@Component({
  selector: 'lib-user',
  imports: [CommonModule, SharedModule],
  templateUrl: './user.component.html',
  styleUrl: './user.component.css',
})
export class UserComponent {
  header = 'User';

  loggedInEmployee = decryptObject(localStorage.getItem('logged-in-employee') ?? '', ENCRYPTION_KEY);
  private authService = inject(AuthService);

  get employee(): any {
    return this.loggedInEmployee;
  }


  async onLogout(): Promise<void> {
    try {
      await this.authService.logout();
      console.log('Successfully logged out');
    } catch (error) {
      console.error('Error during logout:', error);
    }
  }

  onDefaultStore(): void {
    console.log('Default store clicked');
  }
}
