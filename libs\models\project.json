{"name": "models", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/models/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/models/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/models/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/models/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/models/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}