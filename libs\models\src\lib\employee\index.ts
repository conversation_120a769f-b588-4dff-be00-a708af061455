export interface Employee {
  first_name: string;
  employee_name: string;
  status: string;
  company: string;
  employee_number: string;
  designation: string;
  branch: string;
}

export interface EmployeeResponse {
  data: Employee[];
}

export interface User {
  system_user: 'yes' | 'no';
  full_name: string;
  user_id: string;
  user_image: string;
}

export interface OpenIdProfile {
  sub: string;
  name: string;
  given_name: string;
  family_name: string;
  email: string;
  iss: string;
  picture: string;
  roles: string[];
}
