export interface FrappeListResponse<T> {
  data: T[];
}

export interface FrappeSingleResponse<T> {
  data: T;
}

/**
 * Filter operator types
 */
export enum FilterOperator {
  EQUALS = '=',
  NOT_EQUALS = '!=',
  GREATER_THAN = '>',
  LESS_THAN = '<',
  LIKE = 'like'
}

/**
 * Filter condition type (field, operator, value)
 */
export type FilterCondition = [string, FilterOperator, string | number | boolean];
