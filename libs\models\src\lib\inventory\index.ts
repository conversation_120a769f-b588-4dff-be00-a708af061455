export interface InventoryItem {
  item_code: string;
  actual_qty: number;
  available_qty: number;
  stock_uom: string;
  is_stock_item: boolean;
}

export interface InventoryListResponse {
  data: InventoryItem[];
}

export interface StockReconciliation {
  name: string;
  posting_date: string;
  posting_time: string;
  workflow_state: string;
}

export interface StockReconciliationListResponse {
  data: StockReconciliation[];
}

export interface StockReconciliationItem {
    item_code: string,
    item_name: string,
    description : string,
    item_group: string,
    stock_uom: string,
    disabled: string | number,
    quantity: number,
    image: null
    price?: number
}


export interface CreateStockReconciliation {
  docstatus: number;
  company: string;
  purpose: string;
  posting_date: string;
  posting_time: string;
  branch: string;
  items: StockReconciliationItem[];
}

export interface StockReconciliationResponse {
  data: {
    name: string;
    owner: string;
    creation: string;
    modified: string;
    modified_by: string;
    docstatus: number;
    company: string;
    purpose: string;
    posting_date: string;
    posting_time: string;
    branch: string;
    items: StockReconciliationItem[];
  };
}

export enum StockReconciliationPurpose {
  STOCK_RECONCILIATION = 'Stock Reconciliation',
}

export interface StockReconciliationEntryItem {
  item_code: string,
  warehouse: string;
  qty: number;
}
export interface StockReconcilitionEntry {
  data: {
    docstatus: number;
    company: string;
    purpose: string;
    posting_date: string;
    posting_time: string;
    branch:string,
    items: StockReconciliationEntryItem[]
  }
}