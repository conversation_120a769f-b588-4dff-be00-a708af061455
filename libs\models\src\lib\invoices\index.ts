import { FilterCondition, FrappeListResponse, FrappeSingleResponse } from "../frappe";

export interface POSInvoice {
  id: number;
  currency: string;
  customer: string;
  discount_amount: number;
  grand_total: number;
  rounded_total: number;
  name: string;
  outstanding_amount: number;
  paid_amount: number;
  posting_date: string;
  posting_time: string;
  selling_price_list: string;
  status:string;
}

export interface InvoiceDetail {
  name: string; //Is like a unique id
  customer: string;
  pos_profile: string;
  is_return: 0 | 1;
  docstatus: 0 | 1;
  company: string;
  currency: string;
  set_warehouse: string;
  items: InvoiceDetailItem[];
  payments: InvoiceDetailPayment[];
  status: string;
}

export interface InvoiceDetailPayment {
  name: string;
  mode_of_payment: string;
  amount:number;
  parent:string; // Id of the invoice
}

export interface InvoiceDetailItem {
  item_code: string;
  qty: number;
  rate: number;
  amount: number;
}

export type InvoiceDetailItemResponse = FrappeSingleResponse<InvoiceDetailItem>;

export type POSInvoiceResponse  = FrappeListResponse<POSInvoice>

export type PosOpeningEntryListResponse = FrappeListResponse<PosOpeningEntry>;

export interface POSInvoiceItem {
  item_code: string;
  qty: number;
}

export interface POSInvoicePayment {
  mode_of_payment: string;
  amount: number;
}

export interface POSInvoiceRequest {
  is_pos: number;
  company: string;
  selling_price_list?: string;
  customer: string;
  items: POSInvoiceItem[];
  payments: POSInvoicePayment[];
  is_return?: number | string;
  return_against?: string;
  "docstatus"?: 1
}

export interface PosOpeningEntry {
  name: string;
  status: PosOpeningStatus;
  period_start_date: string;
  pos_profile: string;
}

export enum PosOpeningStatus {
  DRAFT = 'Draft',
  OPEN = 'Open',
  CLOSED = 'Closed',
  CANCELLED = 'Cancelled'
}

export enum ModeOfPayment {
  CASH = 'cash',
  CARD = 'card',
  MOBILE = 'mobile',
}

export interface POSCheckResponse {
  isLastEntryClosed: boolean;
  hasOpeningEntryToday: boolean;
  mostRecentEntry:PosOpeningEntry | null;
}

export interface BalanceDetail {
  mode_of_payment: string;
  opening_amount: number;
}

export interface PosOpeningEntryCreate {
  data: {
    period_start_date: string;
    posting_date: string;
    company: string;
    pos_profile: string;
    user: string;
    docstatus: number;
    balance_details: BalanceDetail[];
  }
}

/**
 * Request parameters for fetching POS Opening Entries
 */
export interface PosOpeningEntryListRequest {
  fields?: Array<keyof PosOpeningEntry>;
  filters?: FilterCondition[];
  limit?: number;
  offset?: number;
}

export interface PaymentReconciliation {
  mode_of_payment: string;
  opening_amount: number;
  expected_amount: number;
  closing_amount: number;
}

export interface POSTransaction {
  pos_invoice: string;
}

// Represents a single balance detail entry within POS Opening Entry
export interface POSOpeningEntryDetail {
  name: string;
  owner: string;
  creation: string;
  modified: string;
  modified_by: string;
  docstatus: number;
  idx: number;
  mode_of_payment: string;
  opening_amount: number;
  parent: string;
  parentfield: string;
  parenttype: string;
  doctype: "POS Opening Entry Detail";
}

// Extends the base PosOpeningEntry with full details
export interface POSOpeningEntryDetailResponse extends PosOpeningEntry {
  owner: string;
  creation: string;
  modified: string;
  modified_by: string;
  docstatus: number;
  idx: number;
  set_posting_date: number;
  pos_closing_entry?: string;
  balance_details: POSOpeningEntryDetail[];
}


