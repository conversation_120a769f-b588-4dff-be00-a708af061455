export interface ItemGroupResponse {
  data: ItemGroup[];
}

export interface ItemGroup {
  name: string;
}

export interface ItemResponse {
  data: Item[];
}

export interface Item {
  item_code: string;
  item_name: string;
  description: string;
  item_group: string;
  stock_uom: string;
  disabled: number;
  image: string | null;
  price?: number;
}
export interface CartItem extends Item {
  quantity: number;
  isEditing?: boolean;
  editQuantity?: number;
}

export interface ItemPrice {
  item_code: string;
  item_name: string;
  price_list_rate: number;
  price_list: string;
}