import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'dateDifference'
})
export class DateDifferencePipe implements PipeTransform {

  transform(targetDateStr: string): number {
    const targetDate = new Date(targetDateStr); 
    const currentDate = new Date();

    const differenceInMs = currentDate.getTime() - targetDate.getTime();
    const differenceInDays = Math.floor(differenceInMs / (1000 * 60 * 60 * 24));

    return differenceInDays;
  }
}
