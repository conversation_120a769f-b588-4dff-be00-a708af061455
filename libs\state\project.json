{"name": "state", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/state/src", "prefix": "lib", "projectType": "library", "tags": [], "implicitDependencies": ["api"], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/state/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/state/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/state/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/state/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}