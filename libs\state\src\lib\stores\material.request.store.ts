import { inject } from '@angular/core';

import { patchState, signalStore, type, withHooks } from '@ngrx/signals';

import { lastValueFrom } from 'rxjs';

import { setAllEntities, withEntities } from '@ngrx/signals/entities';
import { withCallStatus, withEntitiesLocalFilter } from '@ngrx-traits/signals';

import { MaterialRequestService } from '@elevar-clients/api';

const entity = type<any>();

export const MaterialRequestStore = signalStore(
  { providedIn: 'root' },
  withEntities({ entity }),
  withCallStatus({ initialValue: 'loading' }),
  withEntitiesLocalFilter({
    entity,
    defaultFilter: {
      search: '',
    },
    filterFn: (entity, filter) =>
      !filter?.search ||
      Object.values(entity).some(value =>
        typeof value === 'string' && value.toLowerCase().includes(filter.search.toLowerCase())
      ),
  }),

  withHooks(({ setLoaded, setError, ...store }) => ({
    onInit: async () => {
      const materialService = inject(MaterialRequestService);
      try {
        const requests = (await lastValueFrom(materialService.getMaterialRequestsByCompany()));
        const requestsWithIds = requests.data.map((request, index) => ({
          ...request,
          id: index + 1,
        }));
        patchState(store, setAllEntities(requestsWithIds));
        setLoaded();
      } catch (e) {
        setError(e);
      }
    },
  })),
)

