import { inject, signal, computed, WritableSignal } from '@angular/core';
import { patchState, signalStore, type, withState, withMethods } from '@ngrx/signals';
import { lastValueFrom } from 'rxjs';
import { getTimeAgoAndLocation, normalizeTime } from '@core';
import { InventoryService, PosProfileService } from '@elevar-clients/api';
import { StockReconciliation, StockReconciliationListResponse } from '@models';

const PAGE_SIZE = 20;
const entity = type<any>();

export const StockStore = signalStore(
  { providedIn: 'root' },
  withState(() => {
    const reconciliations: WritableSignal<StockReconciliation[]> = signal([]);
    const isLoading = signal(false);
    const isLoadingMore = signal(false);
    const allLoaded = signal(false);
    const currentRange = signal({ start: 0, end: 0, total: 0 });
    const searchQuery = signal('');
    const company = signal<string | null>(null);
    const allReconciliations: WritableSignal<StockReconciliation[]> = signal([]);
    const usingClientSidePagination = signal(false);
    const filteredReconciliations = computed(() => {
      const query = searchQuery().toLowerCase();
      const list = reconciliations();
      if (!query) return list;
      return list.filter(item =>
        Object.values(item).some(val =>
          typeof val === 'string' && val.toLowerCase().includes(query)
        )
      );
    });
    return {
      reconciliations: filteredReconciliations,
      isLoading,
      isLoadingMore,
      allLoaded,
      currentRange,
      searchQuery,
      company,
      allReconciliations,
      usingClientSidePagination,
      _reconciliations: reconciliations // for internal use
    };
  }),
  withMethods((state) => {
    const _inventoryService = inject(InventoryService);
    const _posProfileService = inject(PosProfileService);
    return {
      async loadInitialReconciliations() {
        state.isLoading().set(true);
        state.allLoaded().set(false);
        state._reconciliations().set([]);
        state.currentRange().set({ start: 0, end: 0, total: 0 });
        state.searchQuery().set('');
        // Get company
        const profile = _posProfileService.posProfile();
        if (!profile?.company) {
          state.isLoading().set(false);
          return;
        }
        state.company().set(profile.company);
        try {
          // Try paginated fetch
          const response = await lastValueFrom(_inventoryService.getStockReconciliations(profile.company, 0, PAGE_SIZE));
          if (Array.isArray(response.data)) {
            state._reconciliations().set(response.data);
            state.currentRange().set({ start: 1, end: response.data.length, total: response.data.length });
            if (response.data.length < PAGE_SIZE) {
              state.allLoaded().set(true);
            }
            // If backend returns all at once, fallback to client-side
            if (response.data.length > PAGE_SIZE) {
              state.allReconciliations().set(response.data);
              state.usingClientSidePagination().set(true);
              state._reconciliations().set(response.data.slice(0, PAGE_SIZE));
              state.currentRange().set({ start: 1, end: PAGE_SIZE, total: response.data.length });
              state.allLoaded().set(PAGE_SIZE >= response.data.length);
            }
          } else {
            state._reconciliations().set([]);
            state.allLoaded().set(true);
          }
        } catch (e) {
          state._reconciliations().set([]);
          state.allLoaded().set(true);
        } finally {
          state.isLoading().set(false);
        }
      },
      async loadMoreReconciliations() {
        if (state.isLoadingMore()() || state.allLoaded()()) return;
        state.isLoadingMore().set(true);
        const currentCount = state._reconciliations()().length;
        const comp = state.company()();
        if (!comp) {
          state.isLoadingMore().set(false);
          return;
        }
        try {
          if (state.usingClientSidePagination()()) {
            // Fallback: client-side slicing
            const allData = state.allReconciliations()();
            const nextSlice = allData.slice(currentCount, currentCount + PAGE_SIZE);
            state._reconciliations().set([...state._reconciliations()(), ...nextSlice]);
            state.currentRange().set({ start: 1, end: state._reconciliations()().length, total: allData.length });
            if (state._reconciliations()().length >= allData.length) {
              state.allLoaded().set(true);
            }
          } else {
            // Server-side pagination
            const response = await lastValueFrom(_inventoryService.getStockReconciliations(comp, currentCount, PAGE_SIZE));
            if (Array.isArray(response.data) && response.data.length > 0) {
              state._reconciliations().set([...state._reconciliations()(), ...response.data]);
              state.currentRange().set({ start: 1, end: state._reconciliations()().length, total: state._reconciliations()().length });
              if (response.data.length < PAGE_SIZE) {
                state.allLoaded().set(true);
              }
            } else {
              state.allLoaded().set(true);
            }
          }
        } catch (e) {
          state.allLoaded().set(true);
        } finally {
          state.isLoadingMore().set(false);
        }
      },
      filterReconciliations(query: string) {
        state.searchQuery().set(query);
      },
      navigateToDetail(router: any, name: string) {
        router.navigateByUrl(`/inventory/details/${name}`);
      },
      navigateToNew(router: any) {
        router.navigate(['/inventory/new-stock-reconciliation']);
      }
    };
  })
);


