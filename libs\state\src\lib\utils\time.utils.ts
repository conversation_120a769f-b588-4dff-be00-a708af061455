function normalizeTime(timeStr: any) {
    return timeStr;
}
  
export function getTimeAgoAndLocation(item: any, company: any) {
const normalizedTime = normalizeTime(item.posting_time);
const postingDate = new Date(`${item.posting_date}T${normalizedTime}`);
const now = new Date();
const diffTime = now.getTime() - postingDate.getTime();

const diffMinutes = Math.floor(diffTime / (1000 * 60));
const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

let timeAgo;
if (diffMinutes < 1) {
    timeAgo = 'Just now';
} else if (diffMinutes < 60) {
    timeAgo = diffMinutes === 1 ? '1 minute ago' : `${diffMinutes} minutes ago`;
} else if (diffHours < 24) {
    timeAgo = diffHours === 1 ? '1 hour ago' : `${diffHours} hours ago`;
} else if (diffDays === 1) {
    timeAgo = 'Yesterday';
} else {
    timeAgo = `${diffDays} days ago`;
}

const location = company.split(' ')[0];

return { timeAgo, location };
}
