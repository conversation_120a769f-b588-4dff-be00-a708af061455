module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          light: 'var(--color-primary-light)',
          DEFAULT: 'var(--color-primary)',
          dark: 'var(--color-primary-dark)',
          lightGreen: 'var(--color-primary-light-green)',
          hoverGreen: 'var(--color-primary-dark-green)',       
          darkBlue: 'var(--color-dark-blue)',
          lightBlue: 'var(--color-light-blue)',
          hoverBlue: 'var(--color-hover-blue)',
          cardGray: 'var(--color-card-gray)',
          greenLight: 'var(--color-lighter-green)',
          'success-green-background': 'var(--color-button-light-green)',
          'success-green-text': 'var(--color-button-dark-green)',
        },
        secondary: {
          light: 'var(--color-secondary-light)',
          DEFAULT: 'var(--color-secondary)',
          dark: 'var(--color-secondary-dark)',
        },
        white: 'var(--color-white)',
        black: 'var(--color-black)',
      },
      spacing: {
        sm: 'var(--spacing-sm)',
        md: 'var(--spacing-md)',
        lg: 'var(--spacing-lg)',
        xl: 'var(--spacing-xl)',
      },
    },
  },
  plugins: [],
};