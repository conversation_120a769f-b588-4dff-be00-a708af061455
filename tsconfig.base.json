{"compileOnSave": false, "compilerOptions": {"sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "es2023", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@core": ["libs/core/src/index.ts"], "@elements/ui": ["libs/elements/ui/src/index.ts"], "@elevar-clients/api": ["libs/api/src/index.ts"], "@features/auth": ["libs/features/auth/src/index.ts"], "@features/inventory": ["libs/features/inventory/src/index.ts"], "@features/invoices": ["libs/features/invoices/src/index.ts"], "@features/reports": ["libs/features/reports/src/index.ts"], "@features/user": ["libs/features/user/src/index.ts"], "@models": ["libs/models/src/index.ts"], "@state": ["libs/state/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}